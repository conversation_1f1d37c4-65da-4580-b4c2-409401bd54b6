package yyy.xxx.simpfw.module.uis.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.itextpdf.forms.PdfPageFormCopier;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.io.image.ImageData;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.canvas.parser.listener.IPdfTextLocation;
import com.itextpdf.layout.Canvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.UnitValue;
import com.itextpdf.layout.properties.VerticalAlignment;
import com.itextpdf.pdfcleanup.PdfCleaner;
import com.itextpdf.pdfcleanup.autosweep.CompositeCleanupStrategy;
import com.itextpdf.pdfcleanup.autosweep.RegexBasedCleanupStrategy;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import yyy.xxx.common.net.storage.AbsFile;
import yyy.xxx.common.net.storage.StorageInterface;
import yyy.xxx.common.net.storage.StorageInterfaceFactory;
import yyy.xxx.common.net.storage.impl.smb.SmbStorage;
import yyy.xxx.common.net.storage.utils.FileUtils;
import yyy.xxx.simpfw.common.annotation.DataSource;
import yyy.xxx.simpfw.common.core.domain.HttpRequestConfig;
import yyy.xxx.simpfw.common.core.domain.SimpleMap;
import yyy.xxx.simpfw.common.core.domain.entity.SysDictData;
import yyy.xxx.simpfw.common.core.domain.entity.SysUser;
import yyy.xxx.simpfw.common.core.domain.model.LoginUser;
import yyy.xxx.simpfw.common.core.redis.RedisCache;
import yyy.xxx.simpfw.common.core.service.HttpClientService;
import yyy.xxx.simpfw.common.enums.DataSourceType;
import yyy.xxx.simpfw.common.utils.DateUtils;
import yyy.xxx.simpfw.common.utils.SecurityUtils;
import yyy.xxx.simpfw.common.utils.http.WebUtil;
import yyy.xxx.simpfw.module.pacs.Const;
import yyy.xxx.simpfw.module.pacs.bo.SignInfo;
import yyy.xxx.simpfw.module.pacs.bo.StorageParam;
import yyy.xxx.simpfw.module.pacs.component.JsonConfigService;
import yyy.xxx.simpfw.module.pacs.constants.FileStatus;
import yyy.xxx.simpfw.module.pacs.constants.FileType;
import yyy.xxx.simpfw.module.pacs.constants.TableMetadata;
import yyy.xxx.simpfw.module.pacs.dict.ExamEnum;
import yyy.xxx.simpfw.module.pacs.dict.ResultStatus;
import yyy.xxx.simpfw.module.pacs.dto.FileInfoDto;
import yyy.xxx.simpfw.module.pacs.dto.RefEntityExample;
import yyy.xxx.simpfw.module.pacs.entity.*;
import yyy.xxx.simpfw.module.pacs.mapper.ExamAttachmentMapper;
import yyy.xxx.simpfw.module.pacs.mapper.ExamInfoMapper;
import yyy.xxx.simpfw.module.pacs.service.*;
import yyy.xxx.simpfw.module.pacs.service.impl.BridgeService;
import yyy.xxx.simpfw.module.pacs.service.impl.DicomImageIndexServiceImpl;
import yyy.xxx.simpfw.module.pacs.service.impl.ExamFileService;
import yyy.xxx.simpfw.module.pacs.service.impl.RefService;
import yyy.xxx.simpfw.module.pacs.utils.CacheUtil;
import yyy.xxx.simpfw.module.pacs.utils.FileUrlDesUtil;
import yyy.xxx.simpfw.module.pacs.utils.PdfUtil;
import yyy.xxx.simpfw.module.pacs.utils.FileUtil;
import yyy.xxx.simpfw.system.service.ISysConfigService;
import yyy.xxx.simpfw.system.service.ISysUserService;

import java.io.*;
import java.net.MalformedURLException;
import java.net.URI;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service("uisExamReportService")
@DataSource(value = DataSourceType.SLAVE)
public class ExamReportServiceImpl implements ExamReportService {
    private static final Logger log = LoggerFactory.getLogger(ExamReportServiceImpl.class);

    @Autowired
    private ExamInfoMapper mapper;

    @Autowired
    private ExamAttachmentMapper attachMapper;

    @Autowired
    private DicomStudyService dicomStudySerice;

    @Autowired
    private CallInfoService callInfoService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private DicomImageService dicomImageService;

    @Autowired
    private HttpClientService httpClient;

    @Autowired
    private EquipRoomService equipRoomService;

    @Autowired
    private DicomStudyService studyService;

    @Autowired
    private ReportInterService reportInterService;

    @Autowired
    private BridgeService bridgeService;

    private final HttpRequestConfig httpRequestConfig = new HttpRequestConfig().setSocketTimeout(10 * 1000);

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ExamInfoService infoService;

    @Autowired
    private DicomImageTransferService dicomImageTransferService;

    @Autowired
    private ReportQRCodeService reportQRCodeService;

    @Autowired
    private DictConvertToCCC dictConvertToCCC;

    @Autowired
    private JsonConfigService jsonConfigService;

    @Autowired
    private ExamFileService examFileService;

    @Autowired
    private RefService refService;

    @Autowired
    private ISysUserService userService;


    /**
     * 保存报告相关的字段
     */
    @Transactional
    public int save(ExamInfo entity) {
        //检查时间
        if (null == entity.getExamTime()) {
            entity.setExamTime(DateUtils.getNowDate());
        }
        //医生
        //
        int num = mapper.saveReport(entity);
        //更新检查机房
//        CallInfo callInfo = callInfoService.selectByExam(entity.getId());
//        if(null == callInfo.getCallRoom() || StringUtils.isBlank(callInfo.getCallRoom().getRoomCode())) {
//            callInfo.setExamInfo(entity);
//            callInfoService.call(callInfo, entity.getEquipRoom().getRoomCode());
//        }
        //保存图像
        saveImages(entity);
        //
//        DicomStudy study = entity.getDicomStudy();
//        if(null != study && org.apache.commons.lang3.StringUtils.isNotBlank(study.getStudyInstanceUid())) {
//            dicomStudySerice.saveOrUpdate(entity.getDicomStudy());
//        }

        return num;
    }

    /**
     * 保存报告的图片
     *
     * @param entity 报告内容
     */
    @Transactional
    public void saveImages(ExamInfo entity) {

        List<ExamAttachment> exists = selectImages(entity);
        //
        List<ExamAttachment> images = entity.getImages();
        //删除
        for (ExamAttachment e : exists) {
            if (null != e.getId() && images.stream().noneMatch(i -> null != i.getId() && e.getId().longValue() == i.getId().longValue())) {
                attachMapper.delete(e.getId());
            }
        }
        //添加
        for(int i=0;i<images.size();i++) {
            ExamAttachment img = images.get(i);
            if (null == img.getId()) {
                img.setExamInfoId(entity.getId());
                img.setType(attTypeImage);
                img.setOrderNum(i);
                attachMapper.insert(img);
            }
        }
    }

    @Transactional
    public int resultStatusRollback(ExamInfo entity) {
        //状态检查，已登记、已检查和书写完成可保存报告
        SysDictData resultStatus = entity.getResultStatus();

        if (ResultStatus.REPORT.is(resultStatus.getDictValue())) {
//            entity.setReportDoctor(null);
//            //info.setReportD(null);
//            entity.setReportTime(null);
//            entity.setExamDesc(null);
//            entity.setExamDiagnosis(null);
//            entity.setOperationSuggestion(null);
//            entity.setExamResultProp(null);
//            entity.setReportUrlOrgPdf(null);

            SysDictData resultStatusS = entity.getResultStatus();
            resultStatusS.setDictValue(ResultStatus.EXAM.getValue());
            entity.setResultStatus(resultStatusS);
//            examFileService.removeFiles(entity.getExamUid());
        } else if (ResultStatus.EXAM.is(resultStatus.getDictValue())) {
//            entity.setExamDoctor(null);
//            entity.setExamDoctorsName(null);
//            entity.setExamTime(null);

            SysDictData resultStatusS = entity.getResultStatus();
            resultStatusS.setDictValue(ResultStatus.REGIST.getValue());
            entity.setResultStatus(resultStatusS);
        }

//        int num = mapper.update(entity);
        int num = mapper.updateResultStatus(entity);
        return num;
    }

    @Transactional
    public int reportDataClear(ExamInfo entity) {

        entity.setExamDoctor(null);
        entity.setExamDoctorsName(null);
        entity.setExamTime(null);

        entity.setReportDoctor(null);
        //info.setReportD(null);
        entity.setReportTime(null);
        entity.setAuditDoctor(null);
        entity.setAuditTime(null);
        entity.setSignTime(null);
        entity.setSecondAuditDoctor(null);
        entity.setSecondAuditTime(null);
        entity.setThirdAuditDoctor(null);
        entity.setThirdAuditTime(null);
        entity.setExamDesc(null);
        entity.setExamDiagnosis(null);
        entity.setOperationSuggestion(null);
        entity.setExamResultProp(null);
        entity.setReportUrlOrgPdf(null);
        entity.setReportUploadFilename(null);
        entity.setExamResultProp(null);

        int num = mapper.update(entity);

        examFileService.updateStatusByExamUid(entity.getExamUid(), FileStatus.DELETED);

        RefEntityExample example = new RefEntityExample();
        example.setRefTableName(TableMetadata.R_EXAM_INFO_EXAM_RESULT);
        example.createCriteria().andObjectId1EqualTo(Long.valueOf(entity.getExamSerialNo()));
        refService.delete(example);
        return num;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<ExamAttachment> selectImages(ExamInfo ei) {
        ExamAttachment att = new ExamAttachment();
        att.setExamInfoId(ei.getId());
        att.setType(attTypeImage);
        att.setStatus(ExamAttachment.STATUS_NORMAL);
        List<ExamAttachment> items = attachMapper.selectList(att);
        //检查
        if (null != items) {
            for (int i = items.size() - 1; i >= 0; i--) {
                ExamAttachment item = items.get(i);
                String path = item.getPath();
                if (StringUtils.isBlank(path)) {
                    attachMapper.delete(item.getId());
                }

                if (Const.FILE_TYPE_NAME_DCM.equals(item.getFileType())) {
                    continue;
                }

                try {
                    String sop = path.split(Const.slash)[2];
                    if (null == dicomImageService.selectSop(sop)) {
                        log.warn("无效报告影像 exam.id={}, examNo={}, atta.id={}, atta.path={}"
                                , ei.getId(), ei.getExamNo(), item.getId(), path);
                        attachMapper.delete(item.getId());
                        items.remove(i);
                    }
                } catch (Exception err) {
                    items.remove(i);
                }
            }
        }

        return items;
    }

    @Transactional
    public int audit(ExamInfo entity) {
        //先保存
        //mapper.saveReport(entity);
        save(entity);
        //再审核
        return mapper.auditReport(entity);
    }

    public int reaudit(ExamInfo param) {
        return mapper.reauditReport(param);
    }

    @Transactional
    public int sign(ExamInfo entity, String signImg) throws Exception {
        int ordNum = 1;
        String resultStatus = entity.getResultStatus().getDictValue();
        if (ResultStatus.SECOND_AUDIT.is(resultStatus)) {
            ordNum = 2;
        } else if (ResultStatus.THIRD_AUDIT.is(resultStatus)) {
            ordNum = 3;
        }
        //将签字图片作为附件
        attachMapper.deleteByExamAndOrdNum(entity.getId(), attTypeSign, ordNum);

        ExamAttachment att = new ExamAttachment();
        att.setExamInfoId(entity.getId());
        att.setType(attTypeSign);
        att.setFileType("png");
        att.setData(signImg.getBytes(StandardCharsets.UTF_8));
        att.setOrderNum(ordNum);
        attachMapper.insert(att);
        //更新签字相关信息
        return mapper.signReport(entity);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int saveDoc(ExamInfo report,SysUser auditDoctor) {
        StorageInterface<?, ?> storage = null;
        try {
            storage = getReportStorage();
            String storageUserinfo = null;
            //pdf报告
            String pdfData = report.getReportUrlPdf();
            File pdfFile = null;
            byte[] pdfContent = null;
            //
            if (StringUtils.isBlank(pdfData) || !yyy.xxx.simpfw.common.utils.StringUtils.validateBase64(pdfData)) {
                //生成pdf
                try {
                    pdfFile = createReportPdf(report);
                } catch (Exception err) {
                    log.error("生成PDF报告错误：检查号={}", report.getExamNo());
                    throw new RuntimeException(err);
                }
            } else {
                //pdf 内容
                pdfContent = yyy.xxx.simpfw.common.utils.StringUtils.base64ToBytes(pdfData);
            }

            try {
                //保存路径+文件名
//                String rFileName = report.getFileName();
//                String filePath;
//                if(null!=rFileName){
//                    rFileName = rFileName.substring(0,rFileName.lastIndexOf("."));
//                    filePath = String.format("%s%s.pdf", folder, rFileName);
//                }else{
//                    filePath = String.format("%s%d.pdf", folder,report.getId());
//                }

                String fileName = report.getExamSerialNo() + ".pdf";
                String filePath = FileUtil.getReportAuditFileUrl(auditDoctor.getDept(), report, fileName);

                String reportUrlPdf = null;
                URI uri = null;
                if (null != pdfFile) {
                    uri = FileUtil.storageFile(Files.newInputStream(pdfFile.toPath()), storage, filePath);
                    reportUrlPdf = URLDecoder.decode(uri.toString(), Const.charset_UTF_8);
                    if (!pdfFile.delete()) {
                        log.info("报告临时文件删除失败{}", pdfFile.getAbsolutePath());
                    }
                } else {
                    uri = FileUtil.storageFile(new ByteArrayInputStream(pdfContent), storage, filePath);
                    reportUrlPdf = URLDecoder.decode(uri.toString(), Const.charset_UTF_8);
                }
                report.setReportUrlPdf(FileUrlDesUtil.encode(reportUrlPdf));
                if (null != uri) storageUserinfo = uri.getUserInfo();

//                AbsFile file;
//                if(null != pdfFile) {
//                    file = storage.write(filePath, Files.newInputStream(pdfFile.toPath()));
//                    if(!pdfFile.delete()) {
//                        log.info("报告临时文件删除失败{}", pdfFile.getAbsolutePath());
//                    }
//                } else {
//                    file = storage.write(filePath, pdfContent);
//                }
//                URI uri = file.toURI(true);
//                report.setReportUrlPdf(URLDecoder.decode(uri.toString(), Const.charset_UTF_8));
//                storageUserinfo = uri.getUserInfo();
            } catch (Exception err) {
                log.error(err.getMessage(), err);
                throw new RuntimeException(err);
            }

            //jpg报告
            String jpgData = report.getReportUrlJpg();
            if (StringUtils.isNotBlank(jpgData) && yyy.xxx.simpfw.common.utils.StringUtils.validateBase64(jpgData)) {
                byte[] bytes = yyy.xxx.simpfw.common.utils.StringUtils.base64ToBytes(jpgData);
                try {
                    String fileName = report.getExamSerialNo() + ".jpg";
                    String filePath = FileUtil.getReportAuditFileUrl(auditDoctor.getDept(),report, fileName);

                    URI uri = FileUtil.storageFile(new ByteArrayInputStream(bytes), storage, filePath);
                    String reportUrlJpg = URLDecoder.decode(uri.toString(), Const.charset_UTF_8);
                    report.setReportUrlJpg(reportUrlJpg);
                    if (null != uri) storageUserinfo = uri.getUserInfo();

//                    AbsFile file = storage.write(filePath, bytes);
//                    URI uri = file.toURI(true);
//                    report.setReportUrlJpg(URLDecoder.decode(uri.toString(), Const.charset_UTF_8));
//                    storageUserinfo = uri.getUserInfo();
                } catch (Exception err) {
                    log.error(err.getMessage(), err);
                    throw new RuntimeException(err);
                }
            }
            if (StringUtils.isNotBlank(storageUserinfo)) {
                String[] up = storageUserinfo.split(Const.SYMBOL_COLON);
                report.setReportUrlUsername(up[0]);
                if (up.length > 1) {
                    report.setReportUrlPassword(up[1]);
                }
            }
        } catch (Exception e) {
            throw new IllegalArgumentException(String.format("报告保存异常，报告id：{}", report.getId()));
        } finally {
            storage.release();
        }

        return mapper.saveDoc(report);
    }

    @DataSource(value = DataSourceType.MASTER)
    private String getSysConfig(String key) {
        return configService.selectConfigByKey(key);
    }

    /**
     * 生成报告文件
     *
     * @param report
     * @return
     * @throws Exception
     */
    private File createReportPdf(ExamInfo report) throws Exception {
        //报告输出临时路径
        String destFileName = String.format("%d-%s-%d.pdf", report.getId(), report.getExamNo(), System.currentTimeMillis());
        Path destFilePath = FileUtils.getDir(String.format("temp/report/%s", DateUtils.getDate())).resolve(destFileName);
        //File destFile = new File(FileUtils.getDir(String.format("temp/report/%s", DateUtils.getDate())).toAbsolutePath().toFile(), destFileName);
        if (log.isInfoEnabled()) {
            log.info("检查号={}, 检查报告={}", report.getExamNo(), destFilePath);
        }

        String templateWay = jsonConfigService.gettemplateWay(report.getExamItem().getDictValue());

//            if(report.getExamItem().getDictValue().equals("Otol_REM")||report.getExamItem().getDictValue().equals("Otol_PTA")||report.getExamItem().getDictValue().equals("NYST")){
        if (null != templateWay && (templateWay.equals("OnlyStructTemplate") || templateWay.equals("EndOfStructTemplate"))) {
            createDoc2(Files.newOutputStream(destFilePath), report, false, templateWay);
        } else {
            createDoc(Files.newOutputStream(destFilePath), report, false, templateWay);
        }

        //程序退出时删除本地报告文件
        File destFile = destFilePath.toFile();
        destFile.deleteOnExit();

        return destFile;
    }

    /**
     * 获取报告PDF
     * @param report
     * @return
     */
    public byte[] getReportAuditPdf(ExamInfo report){
        byte[] ret = new byte[0];
        String path = report.getReportUrlPdf();
        return ret;
    }

    public void readReportImages(ExamInfo report) {
        ExamAttachment attaParam = new ExamAttachment();
        attaParam.setExamInfoId(report.getId());
        attaParam.setType("report::image");
        List<ExamAttachment> list = attachMapper.selectList(attaParam);
        report.setImages(attachMapper.selectList(attaParam));
    }

    /**
     * 读取报告图像
     *
     * @param atta
     * @return
     * @throws Exception
     */
    public byte[] readReportImageData(ExamAttachment atta) throws Exception {
        String imagePath = atta.getPath();
        if (null == imagePath) {
            return null;
        }
        //dicom
        // /ext/dicom-web/rs/studies/DCM..1.2.840.113663.1500.1.458618363.1.1.20221020.155919.49/series/1.2.840.113663.1500.1.458618363.2.1.20221020.155919.50/instances/1.2.840.113663.1500.1.458618363.3.5.20221020.160054.315/frames/1
        // /dic2png?SOPInstanceUID=1.2.840.113663.1500.1.458618363.3.5.20221020.160054.315
        if (Const.FILE_TYPE_NAME_DCM.equals((atta.getFileType()))) {
            String sopInstanceUid = imagePath.replaceAll("^.+/([^/]+)/frames/\\d+$", "$1");
            if (StringUtils.isBlank(sopInstanceUid) || sopInstanceUid.equals(imagePath)) {
                return null;
            }
            //调用dicom服务获取图像
            String imageIndexServer = getSysConfig(DicomImageIndexServiceImpl.CFG_KEY_INDEXSERVER);
            if (StringUtils.isNotBlank(imageIndexServer)) {
                try {
                    //获取jpg/png格式数据
                    String url = String.format("%s/dic2png?SOPInstanceUID=%s", imageIndexServer, sopInstanceUid);
                    if (log.isDebugEnabled()) {
                        log.debug("读取报告图像 spath={}, tpath={}", imagePath, url);
                    }
                    try (ByteArrayOutputStream stream = new ByteArrayOutputStream(8192);) {
                        Map<String, Object> headers = new SimpleMap().set("Content-Type", WebUtil.MINETYPE_STREAM);
                        httpClient.doGetForStream(url, null, headers, httpRequestConfig, stream);
                        return stream.toByteArray();
                    }
                } catch (Exception err) {
                    log.error(String.format("生成报告图像错误path=%s, ", imagePath), err);
                }
            }
        }
        //jpg路径为三段d_dicom_image表StudyInstanceUID/SeriesInstanceUID/SOPInstanceUID
        String[] segm;
        if ((segm = imagePath.split(Const.slash)).length == 3) {
            DicomImage image = dicomImageService.selectSop(segm[2]);
            try (ByteArrayOutputStream stream = new ByteArrayOutputStream(8192);) {
                dicomImageService.retrieveImage(stream, image);
                return stream.toByteArray();
            }
        }
        //
        return null;
    }

    public void readSignImage(ExamInfo report) throws UnsupportedEncodingException {
        ExamAttachment attaParam = new ExamAttachment();
        attaParam.setExamInfoId(report.getId());
        attaParam.setType("report::sign");
        attaParam.setStatus(0);
        List<ExamAttachment> list = attachMapper.selectList(attaParam);
        if (null != list && !list.isEmpty()) {
            report.setSignImage(new String(list.get(0).getData(), StandardCharsets.UTF_8));
            try {
                report.setSecondSignImage(new String(list.get(1).getData(), StandardCharsets.UTF_8));
            } catch (Exception ignored) {}
            try {
                report.setThirdSignImage(new String(list.get(2).getData(), StandardCharsets.UTF_8));
            } catch (Exception ignored) {}
        }
    }

    /**
     * pdf图像
     *
     * @param src
     * @param scaleToWidth
     * @param scaleToHeight
     * @return
     * @throws MalformedURLException
     */
    private static Image createPdfImage(Object src, float scaleToWidth, float scaleToHeight) throws MalformedURLException {
        if (null == src) {
            throw new IllegalArgumentException("图像数据为null。");
        }
        ImageData imageData;
        if (src instanceof String) {
            imageData = ImageDataFactory.create((String) src);
        } else if (src instanceof byte[]) {
            imageData = ImageDataFactory.create((byte[]) src);
        } else {
            throw new IllegalArgumentException("图像数据格式错误。");
        }
        scaleToWidth = scaleToWidth > 0 ? scaleToWidth : imageData.getWidth();
        if (scaleToHeight <= 0)
            scaleToHeight = scaleToHeight == 0 ? imageData.getHeight() : scaleToWidth / imageData.getWidth() * imageData.getHeight();
        return new Image(imageData).scaleAbsolute(scaleToWidth, scaleToHeight);
    }

    @Transactional
    public int mapReportUrl(ExamInfo examInfo) throws Exception {
        examInfo.setReportDataSource(2);
        mapper.uploadReportUpdateExam(examInfo);
        return 0;
    }

    public StorageParam getStorageParam(String storageConfigKey) {
        String cfgVal = this.getSysConfig(storageConfigKey);
        if (StringUtils.isBlank(cfgVal)) {
            throw new IllegalArgumentException(String.format("未设置报告存放位置%s", storageConfigKey));
        }
        StorageParam storageParam = JSON.parseObject(cfgVal, StorageParam.TypeReference);
        if (null == storageParam || StringUtils.isBlank(storageParam.getLoc())) {
            throw new IllegalArgumentException(String.format("无法识别报告存放配置%s", cfgVal));
        }
        if (log.isDebugEnabled()) {
            log.debug("获取存储{}....", storageParam);
        }
        return storageParam;
    }

    @Override
    @Transactional
    public void doAuditTx(ExamInfo examInfo, String signImg) throws Exception {
        save(examInfo);
        auditReport(examInfo); //是否只用调用这个方法就可以，上面的save没必要
        sign(examInfo, signImg);
    }

    private StorageInterface<?, ?> getStorageInterface(String storageConfigKey) {
        StorageParam storageParam = getStorageParam(storageConfigKey);
        //获取存储
        return StorageInterfaceFactory.getStorage(storageParam.getLoc(), Const.STORAGE_SESSION_OPTION);
    }

    public StorageInterface<?, ?> getVirtualPrintStorage() {
        return getStorageInterface(virtualPrinterStorageConfigKey);
    }

    public StorageInterface<?, ?> getReportStorage() {
        return getStorageInterface(reportStorageConfigKey);
    }

    /**
     * 将文件存至指定存储
     *
     * @param examInfoI 目标的检查/报告
     * @param file      报告文件
     */
    @Transactional
    public int uploadReport(ExamInfo examInfoI, String modalityValue, MultipartFile file, SysUser reportDoc) throws Exception {
        Long examInfoId = examInfoI.getId();
        String reportNo = examInfoI.getReportNo();
        if (null == examInfoId || null == file) throw new IllegalArgumentException("请选择检查和导入的文件。");

        //检查信息
        ExamInfo examInfo = new ExamInfo();
        examInfo.setId(examInfoId);
        examInfo = mapper.selectOne(examInfo);

        //更新检查
        if (null == examInfo.getExamTime()) examInfo.setExamTime(DateUtils.getNowDate());
        //只在书写报告页面，点击选择更改报告医生的时候才触发入库
        /*examInfo.setReportDoctor(reportDoc);
        examInfo.setReportTime(DateUtils.getNowDate());*/
        examInfo.setReportNo(reportNo);
        //更新上传文件名
        examInfo.setReportUploadFilename(examInfoI.getReportUploadFilename());
        //临时set历史文件
        examInfo.setFileInfos(examInfoI.getFileInfos());

        //更新检查状态为已检查
        SysDictData resultStatus = examInfo.getResultStatus();
        resultStatus.setDictValue(ResultStatus.EXAM.getValue());

        //获取报告存储
        StorageInterface<?, ?> storage = getReportStorage();

        String fileName = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf('/') + 1);
        String filePath = FileUtil.getReportMergeFileUrl(reportDoc.getDept(),examInfo, fileName);

        int numAffected = 0;
        try {
            URI uri = FileUtil.storageFile(file, storage, filePath);
            String reportUrlOrgPdf = URLDecoder.decode(uri.toString(), Const.charset_UTF_8);
            ++numAffected;


            examInfo.setReportUrlOrgPdf(FileUrlDesUtil.encode(reportUrlOrgPdf));
            examInfo.setReportDataSource(2);
            mapper.uploadReportUpdateExam(examInfo);

        } finally {
            storage.release();
        }
        return numAffected;
    }

    /**
     * 文件传输参数格式统一后删除
     * 将文件存至指定存储
     * @param examInfoI 目标的检查/报告
     * @param file 报告文件
     */
    @Transactional
    public int uploadReportV1(ExamInfo examInfoI,String modalityValue,MultipartFile file, SysUser reportDoc) throws Exception {
        Long examInfoId = examInfoI.getId();
        String reportNo = examInfoI.getReportNo();
        if(null == examInfoId || null == file) throw new IllegalArgumentException("请选择检查和导入的文件。");

        //检查信息
        ExamInfo examInfo = new ExamInfo();
        examInfo.setId(examInfoId);
        examInfo = mapper.selectOne(examInfo);

        //更新检查
        if(null==examInfo.getExamTime()) examInfo.setExamTime(DateUtils.getNowDate());
        examInfo.setReportDoctor(reportDoc);
        examInfo.setReportTime(DateUtils.getNowDate());
        examInfo.setReportNo(reportNo);
        //更新上传文件名
        examInfo.setReportUploadFilename(examInfoI.getReportUploadFilename());

        SysDictData resultStatus = examInfo.getResultStatus();

        resultStatus.setDictValue(ResultStatus.REPORT.getValue());

        //获取报告存储
        StorageInterface<?, ?> storage = getReportStorage();

        String fileName = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf('/')+1);
        String filePath = FileUtil.getReportMergeFileUrl(reportDoc.getDept(),examInfo,fileName);

        int numAffected = 0;
        try {
            filePath = FileUtils.join(storage.getWorkPath(), filePath);
            URI uploadFileFullPath = FileUtil.storageFile(file,storage,filePath);
            String reportUrlOrgPdf = URLDecoder.decode(uploadFileFullPath.toString(), Const.charset_UTF_8);
            examInfo.setReportUrlOrgPdf(FileUrlDesUtil.encode(reportUrlOrgPdf));
            ++ numAffected;

            examInfo.setReportDataSource(2);
            mapper.uploadReportUpdateExam(examInfo);
        }  catch (Exception err) {
            throw err;
        }finally {
            storage.release();
        }
        return numAffected;
    }

    public MultipartFile manipulatePdf(List<MultipartFile> fileList) throws Exception {
        if (fileList.isEmpty()) {
            return null;
        }
        if (fileList.size() == 1) {
            return fileList.get(0);
        }
        MultipartFile fileBase = fileList.get(0);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        PdfReader reader = new PdfReader(fileBase.getInputStream());
        PdfWriter writer = new PdfWriter(outputStream);
        PdfDocument pdfDoc = new PdfDocument(reader, writer);
        Document doc = new Document(pdfDoc);

        pdfDoc.initializeOutlines();
        PdfPageFormCopier formCopier = new PdfPageFormCopier();

        for (int i = 1; i < fileList.size(); i++) {
            PdfReader reader2 = new PdfReader(fileList.get(i).getInputStream());
            PdfDocument tocDoc = new PdfDocument(reader2);
            tocDoc.copyPagesTo(1, tocDoc.getNumberOfPages(), pdfDoc, formCopier);
            tocDoc.close();
        }


        doc.close();
        InputStream inputStreamW = new ByteArrayInputStream(outputStream.toByteArray());

        MultipartFile File = new MockMultipartFile(fileBase.getName(), fileBase.getOriginalFilename(), fileBase.getContentType(), inputStreamW);
        return File;
    }

    public MultipartFile manipulateExamPdf(MultipartFile file, ExamInfo param) throws Exception {
        ByteArrayOutputStream outputStreamUrl = new ByteArrayOutputStream();
        String reportUrl = param.getReportUrlOrgPdf();

        if (StringUtils.isBlank(reportUrl)) {
            return file;
        }
        reportUrl = yyy.xxx.common.net.storage.utils.WebUtil.putUserinfo(reportUrl, param.getReportUrlUsername(), param.getReportUrlPassword());
        StorageInterface<?, ?> storage = StorageInterfaceFactory.getStorage(reportUrl, Const.STORAGE_SESSION_OPTION);
        try {
            storage.read(reportUrl, outputStreamUrl);
        } catch (Exception err) {
            //logger.error(err.getMessage(), err);
            throw err;
        } finally {
            storage.release();
        }
        InputStream inputStreamW = new ByteArrayInputStream(outputStreamUrl.toByteArray());

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        PdfReader reader = new PdfReader(inputStreamW);
        PdfWriter writer = new PdfWriter(outputStream);
        PdfDocument pdfDoc = new PdfDocument(reader, writer);
        Document doc = new Document(pdfDoc);

        pdfDoc.initializeOutlines();
        PdfPageFormCopier formCopier = new PdfPageFormCopier();


        PdfReader reader2 = new PdfReader(file.getInputStream());
        PdfDocument tocDoc = new PdfDocument(reader2);
        tocDoc.copyPagesTo(1, tocDoc.getNumberOfPages(), pdfDoc, formCopier);
        tocDoc.close();


        doc.close();
        InputStream inputStreamOut = new ByteArrayInputStream(outputStream.toByteArray());

        MultipartFile File = new MockMultipartFile(file.getName(), file.getOriginalFilename(), file.getContentType(), inputStreamOut);
        return File;
    }

    private static Map<String, PdfDocument> initializeFilesToMerge(MultipartFile file2) throws IOException {
        Map<String, PdfDocument> filesToMerge = new TreeMap<String, PdfDocument>();

        try (PdfReader reader2 = new PdfReader(file2.getInputStream());
             PdfDocument pdfDocument2 = new PdfDocument(reader2);) {

            filesToMerge.put("01 Countries", pdfDocument2);

        }

        return filesToMerge;
    }


    public InputStream addAndUp(MultipartFile file, String destPath, ExamInfo examInfo) throws Exception {
        String fontProgram = null;

        if (isLinux()) {
            //linux 楷体
            fontProgram = "/usr/share/fonts/simsun/simsun.ttc";
        } else {
            //windows 楷体
            fontProgram = "C:/Windows/Fonts/simsun.ttc";
        }

        Path tplFile = FileUtils.createTmpFile(destPath, ".pdf");
        File toFile = tplFile.toFile();


        PdfFont font;
        if (new File(fontProgram).isFile()) {
            //windows字体
            font = PdfFontFactory.createFont(fontProgram + ",1", PdfEncodings.IDENTITY_H);
        } else {
            //itext字体，宋体
            font = PdfFontFactory.createFont("STSong-Light", "UniGB-UCS2-H", PdfFontFactory.EmbeddingStrategy.PREFER_EMBEDDED);
        }
        if (log.isDebugEnabled()) {
            log.debug("输出pdf 字体ok.");
        }
        //文本颜色
        Color fontColor = new DeviceRgb(16, 16, 16);
        //字体大小
        int metaFontSize = 10, bodyHeadFontSize = metaFontSize + 2, bodyFontSize = metaFontSize + 1;
        //读取模板，输出报告
        try (PdfReader reader = new PdfReader(file.getInputStream()); PdfWriter writer = new PdfWriter(toFile);
             PdfDocument pdfDocument = new PdfDocument(reader, writer);) {
            try (Document document = new Document(pdfDocument);) {
                //文字
                document.setFont(font).setFontColor(fontColor);


                //检查图像，检查所见，检查诊断
                Table table = new Table(UnitValue.createPercentArray(1));
                table.setBorder(Border.NO_BORDER);
                Cell cell = new Cell();
                cell.setBorder(Border.NO_BORDER);
                cell.setFixedPosition(0, 0, 0);
                //cell.setBorder(Border.NO_BORDER);
                //检查所见
                Paragraph para = new Paragraph("报告编码：" + examInfo.getReportNo()).setFontSize(bodyHeadFontSize);
                cell.add(para);
                table.addCell(cell);
                if (log.isDebugEnabled()) {
                    log.debug("输出pdf 诊断ok.");
                }
                //
                document.add(table);
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("输出pdf end.");
        }
        InputStream inputStream = new FileInputStream(toFile);
        return inputStream;
    }

    public static boolean isLinux() {
        return System.getProperty("os.name").toLowerCase().contains("linux");
    }

    public static boolean isWindows() {
        return System.getProperty("os.name").toLowerCase().contains("windows");
    }

    public Table getJpgTable(ExamInfo report, int mainPos, float pageWidth, float pageHeight, float pageLpad, float pageRpad, int index) throws Exception {
        SysDictData examModality = report.getExamModality(), examItem = report.getExamItem();
        //字体大小
        int metaFontSize = 10, bodyHeadFontSize = metaFontSize + 2, bodyFontSize = metaFontSize + 1;
        //检查图像，检查所见，检查诊断
        Table table = new Table(UnitValue.createPercentArray(1)).useAllAvailableWidth();
        table.setMarginTop(mainPos);//表格顶部位置
        table.setMarginBottom(36);//超出部分不覆盖页脚
        table.setBorder(Border.NO_BORDER);

        //
        Cell cell = new Cell();
        cell.setBorder(Border.NO_BORDER);

        //图像
        List<ExamAttachment> images = report.getImages();
        if (null == images) {
            readReportImages(report);
            images = report.getImages();
        }

        ExamAttachment imgTCDExamData = null;

        //图像数，图像行数
        int numImages = images.size();
        int numImagesPerLine = numImages <= 3 ? numImages : (4 == numImages ? 2 : 3);
        float imageStartX = 60, imageHoriSpace = 10, imageVertSpace = 10;
        //图像尺寸
        float fixedImageWidth = Math.min(230, (pageWidth - (2 * imageStartX) - (numImagesPerLine - 1) * imageHoriSpace) / numImagesPerLine);
        float fixedImageHeight = numImagesPerLine > 1 ? 130 : -1;

        if (examModality.getDictValue().equals("BC") || examModality.getDictValue().equals("INF") || examItem.getDictValue().equals("TCD-I") || examItem.getDictValue().equals("TCD-st") || examItem.getDictValue().equals("TCD-fp") || examItem.getDictValue().equals("TCD-pt") || examItem.getDictValue().equals("Otol_REM") || examItem.getDictValue().equals("Otol_PTA")) {
            table.setMarginTop(2);//表格顶部位置
            table.setMarginBottom(36);//超出部分不覆盖页脚
            //营养成分分析
            fixedImageWidth = pageWidth;
            fixedImageHeight = pageHeight;
            numImagesPerLine = 1;
        }

        if (examItem.getDictValue().equals("Otol_REM")) {
            table.setMarginTop(2);//表格顶部位置
            table.setMarginBottom(36);//超出部分不覆盖页脚
            //营养成分分析
            fixedImageWidth = 623;
            fixedImageHeight = 454;
            numImagesPerLine = 1;
        }

        if (numImagesPerLine == 1) {
            cell.setVerticalAlignment(VerticalAlignment.MIDDLE);
            cell.setHorizontalAlignment(HorizontalAlignment.CENTER);
            Image image = createPdfImage(readReportImageData(images.get(index)), fixedImageWidth, fixedImageHeight);
            image.setHorizontalAlignment(HorizontalAlignment.CENTER);
            image.setMarginLeft(-4);
            if (examModality.getDictValue().equals("BC") || examModality.getDictValue().equals("INF") || examItem.getDictValue().equals("TCD-I") || examItem.getDictValue().equals("TCD-st") || examItem.getDictValue().equals("TCD-fp") || examItem.getDictValue().equals("TCD-pt") || examItem.getDictValue().equals("Otol_PTA")) {
                image.setMarginLeft(-40);
                image.setMarginTop(-35);
            }
            cell.add(image);
        } else if (numImagesPerLine > 1) {
            Table nestTable = new Table(UnitValue.createPercentArray(numImagesPerLine)).useAllAvailableWidth();
            for (int i = 0; i < numImages; i++) {
                Cell c = new Cell();
                c.setBorder(Border.NO_BORDER);
                c.setVerticalAlignment(VerticalAlignment.MIDDLE);
                c.setHorizontalAlignment(HorizontalAlignment.CENTER);

                Image image = createPdfImage(readReportImageData(images.get(i)), fixedImageWidth, fixedImageHeight);
                image.setHorizontalAlignment(HorizontalAlignment.CENTER);
                c.add(image);

                nestTable.addCell(c);
            }
            float marLeft = pageWidth - pageLpad - pageRpad
                    - numImagesPerLine * fixedImageWidth;
            marLeft /= 2;
            nestTable.setMarginLeft(marLeft);
            nestTable.setBorder(Border.NO_BORDER);
            cell.add(nestTable);
        }
        table.addCell(cell);

        if (log.isDebugEnabled()) {
            log.debug("输出pdf 图像ok.");
        }                //
        return table;
    }

    public Table getJpgTable1(ExamInfo report, int mainPos, float pageWidth, float pageHeight, float pageLpad, float pageRpad, int index, byte[] data) throws Exception {
        SysDictData examModality = report.getExamModality(), examItem = report.getExamItem();
        //字体大小
        int metaFontSize = 10, bodyHeadFontSize = metaFontSize + 2, bodyFontSize = metaFontSize + 1;
        //检查图像，检查所见，检查诊断
        Table table = new Table(UnitValue.createPercentArray(1)).useAllAvailableWidth();
        table.setMarginTop(mainPos);//表格顶部位置
        table.setMarginBottom(36);//超出部分不覆盖页脚
        table.setBorder(Border.NO_BORDER);

        //
        Cell cell = new Cell();
        cell.setBorder(Border.NO_BORDER);

        //图像
        List<ExamAttachment> images = report.getImages();
        if (null == images) {
            readReportImages(report);
            images = report.getImages();
        }

        ExamAttachment imgTCDExamData = null;

        //图像数，图像行数
        int numImages = images.size();
        int numImagesPerLine = numImages <= 3 ? numImages : (4 == numImages ? 2 : 3);
        float imageStartX = 60, imageHoriSpace = 10, imageVertSpace = 10;
        //图像尺寸
        float fixedImageWidth = Math.min(230, (pageWidth - (2 * imageStartX) - (numImagesPerLine - 1) * imageHoriSpace) / numImagesPerLine);
        float fixedImageHeight = numImagesPerLine > 1 ? 130 : -1;

        table.setMarginTop(2);//表格顶部位置
        table.setMarginBottom(36);//超出部分不覆盖页脚
        //营养成分分析
        fixedImageWidth = pageWidth;
        fixedImageHeight = pageHeight;
        numImagesPerLine = 1;

//        if(examModality.getDictValue().equals("BC")||examModality.getDictValue().equals("INF")||examItem.getDictValue().equals("TCD-I")||examItem.getDictValue().equals("Otol_REM")||examItem.getDictValue().equals("Otol_PTA")){
//            table.setMarginTop(2);//表格顶部位置
//            table.setMarginBottom(36);//超出部分不覆盖页脚
//            //营养成分分析
//            fixedImageWidth = pageWidth;
//            fixedImageHeight = pageHeight;
//            numImagesPerLine = 1;
//        }
//
//        if(examItem.getDictValue().equals("Otol_REM")){
//            table.setMarginTop(2);//表格顶部位置
//            table.setMarginBottom(36);//超出部分不覆盖页脚
//            //营养成分分析
//            fixedImageWidth = 623;
//            fixedImageHeight = 454;
//            numImagesPerLine = 1;
//        }else if(examModality.getDictValue().equals("Otol")&&examItem.getDictValue().equals("NYST")){
//            //耳鼻喉 眼震
//            table.setMarginTop(2);//表格顶部位置
//            table.setMarginBottom(36);//超出部分不覆盖页脚
//            //营养成分分析
//            fixedImageWidth = pageWidth;
//            fixedImageHeight = pageHeight;
//            numImagesPerLine = 1;
//        }

        if (numImagesPerLine == 1) {
            cell.setVerticalAlignment(VerticalAlignment.MIDDLE);
            cell.setHorizontalAlignment(HorizontalAlignment.CENTER);
            Image image = createPdfImage(data, fixedImageWidth, fixedImageHeight);
            image.setHorizontalAlignment(HorizontalAlignment.CENTER);
            image.setMarginLeft(-4);
            //if(examModality.getDictValue().equals("BC")||examModality.getDictValue().equals("INF")||examItem.getDictValue().equals("TCD-I")||examItem.getDictValue().equals("Otol_PTA")||examItem.getDictValue().equals("NYST")){
            image.setMarginLeft(-38);
            image.setMarginTop(-35);
            //}
            cell.add(image);
        } else if (numImagesPerLine > 1) {
            Table nestTable = new Table(UnitValue.createPercentArray(numImagesPerLine)).useAllAvailableWidth();
            for (int i = 0; i < numImages; i++) {
                Cell c = new Cell();
                c.setBorder(Border.NO_BORDER);
                c.setVerticalAlignment(VerticalAlignment.MIDDLE);
                c.setHorizontalAlignment(HorizontalAlignment.CENTER);

                Image image = createPdfImage(data, fixedImageWidth, fixedImageHeight);
                image.setHorizontalAlignment(HorizontalAlignment.CENTER);
                c.add(image);

                nestTable.addCell(c);
            }
            float marLeft = pageWidth - pageLpad - pageRpad
                    - numImagesPerLine * fixedImageWidth;
            marLeft /= 2;
            nestTable.setMarginLeft(marLeft);
            nestTable.setBorder(Border.NO_BORDER);
            cell.add(nestTable);
        }
        table.addCell(cell);

        if (log.isDebugEnabled()) {
            log.debug("输出pdf 图像ok.");
        }                //
        return table;
    }

    public Table getTable(ExamInfo report, int mainPos, float pageWidth, float pageLpad, float pageRpad) throws Exception {
        SysDictData examModality = report.getExamModality(), examItem = report.getExamItem();
        //字体大小
        int metaFontSize = 10, bodyHeadFontSize = metaFontSize + 2, bodyFontSize = metaFontSize + 1;
        //检查图像，检查所见，检查诊断
        Table table = new Table(UnitValue.createPercentArray(1)).useAllAvailableWidth();
        table.setMarginTop(mainPos);//表格顶部位置
        table.setMarginBottom(36);//超出部分不覆盖页脚
        table.setBorder(Border.NO_BORDER);

        //
        Cell cell = new Cell();
        cell.setBorder(Border.NO_BORDER);

        //图像
        List<ExamAttachment> images = report.getImages();
        if (null == images) {
            readReportImages(report);
            images = report.getImages();
        }

        //图像数，图像行数
        int numImages = images.size();
        int imageRIndex = 0;

        ExamAttachment imgTCDExamData = null;
        //脑多普勒检查数据
        if (examModality.getDictValue().equals("TCD")) {
            if (examItem.getDictValue().equals("TCD-desk")) {
                table.setMarginTop(78);//表格顶部位置
                table.setMarginBottom(13);//超出部分不覆盖页脚

                String examDesc2 = report.getOperationSuggestion();
                examDesc2 = null == examDesc2 ? "" : examDesc2;
                String contentRowData[] = examDesc2.split("\n");
                List<List<String>> RowList = new ArrayList<>(contentRowData.length);
                int maxCol = 0;
                for (int i = 0; i < contentRowData.length; i++) {
                    String contentColData[] = contentRowData[i].split("\t");
                    List<String> ColList = new ArrayList<>(contentColData.length);
                    for (String tlb : contentColData) {
                        ColList.add(tlb);
                    }
                    RowList.add(ColList);
                    if (contentColData.length > maxCol) maxCol = contentColData.length;
                }

                Table table1 = new Table(UnitValue.createPercentArray(maxCol)).useAllAvailableWidth();
                table1.setMarginTop(0);//表格顶部位置
                table1.setMarginBottom(0);//超出部分不覆盖页脚
                table1.setBorder(Border.NO_BORDER);
                for (int i = 0; i < RowList.size(); i++) {
                    for (int j = 0; j < maxCol; j++) {
                        Cell cellImg = new Cell();
                        cellImg.setBorder(Border.NO_BORDER);
                        cellImg.setPadding(-2);
                        Paragraph p;
                        if (j < RowList.get(i).size()) {
                            p = new Paragraph(RowList.get(i).get(j));
                        } else {
                            p = new Paragraph("");
                        }
                        p.setFontSize(bodyHeadFontSize - 2);
                        cellImg.add(p);
                        table1.addCell(cellImg);
                    }
                }
                cell.add(table1);
                table.addCell(cell);
            } else if (examItem.getDictValue().equals("TCD-lap")) {
                if (images.size() > 0) {
                    //Cell cellImg = new Cell();
                    cell.setBorder(Border.NO_BORDER);
                    imgTCDExamData = images.get(0);
                    //images.remove(0);
                    cell.setVerticalAlignment(VerticalAlignment.MIDDLE);
                    cell.setHorizontalAlignment(HorizontalAlignment.CENTER);
                    cell.setMarginLeft(-4);
                    Image image = createPdfImage(readReportImageData(imgTCDExamData), pageWidth - 60, -1);
                    image.setHorizontalAlignment(HorizontalAlignment.CENTER);
                    image.setMarginLeft(-4);
                    cell.add(image);
                    //table.addCell(cellImg);

                    numImages--;
                    imageRIndex = 1;
                }
            }
        }

        int numImagesPerLine = numImages <= 3 ? numImages : (4 == numImages ? 2 : 3);
        float imageStartX = 60, imageHoriSpace = 10, imageVertSpace = 10;
        //图像尺寸
        float fixedImageWidth = Math.min(230, (pageWidth - (2 * imageStartX) - (numImagesPerLine - 1) * imageHoriSpace) / numImagesPerLine);
        float fixedImageHeight = numImagesPerLine > 1 ? 130 : -1;

        //甲测图像大小
        if (examModality.getDictValue().equals("JC")) {
            fixedImageWidth = 540;//Math.min(230, (pageWidth - (2 * imageStartX) - (numImagesPerLine - 1) * imageHoriSpace) / numImagesPerLine);
            fixedImageHeight = 230;//numImagesPerLine > 1? 130 : -1;
        }//脑多普勒图像大小
        else if (examModality.getDictValue().equals("TCD")) {
            imageStartX = 5;
            imageHoriSpace = 2;
            if (1 == numImages && !examItem.getDictValue().equals("TCD-fpsy")) {
                fixedImageWidth = pageWidth - 60;
                fixedImageHeight = -1;
            } else {
                fixedImageWidth = Math.min(280, (pageWidth - 60 - (2 * imageStartX) - (numImagesPerLine - 1) * imageHoriSpace) / numImagesPerLine);
                fixedImageHeight = -1;
            }
        } else if (examModality.getDictValue().equals("Otol") && examItem.getDictValue().equals("NYST")) {
            //耳鼻喉 眼震
            fixedImageWidth = pageWidth - 20;
            fixedImageHeight = 650;

            String designImg = getDesignImg(report);
            if(StringUtils.isNotBlank(designImg)){
                designImg = designImg.replace("data:image/png;base64,","");
                byte[] imageBytes = Base64.getDecoder().decode(designImg);

                cell.setVerticalAlignment(VerticalAlignment.MIDDLE);
                cell.setHorizontalAlignment(HorizontalAlignment.CENTER);
                Image image = createPdfImage(imageBytes, fixedImageWidth, fixedImageHeight);
                image.setHorizontalAlignment(HorizontalAlignment.CENTER);
                image.setMarginLeft(-4);
                cell.add(image);
            }
        }
//        else if(examModality.getDictValue().equals("Otol")&&examItem.getDictValue().equals("Otol_PTA")){
//            //耳鼻喉 眼震
//            fixedImageWidth = pageWidth-20;
//            fixedImageHeight = -1;
//        }

        if (numImagesPerLine == 1) {
            cell.setVerticalAlignment(VerticalAlignment.MIDDLE);
            cell.setHorizontalAlignment(HorizontalAlignment.CENTER);
            Image image = createPdfImage(readReportImageData(images.get(imageRIndex)), fixedImageWidth, fixedImageHeight);
            image.setHorizontalAlignment(HorizontalAlignment.CENTER);
            image.setMarginLeft(-4);
            cell.add(image);
        } else if (numImagesPerLine > 1) {
            Table nestTable = new Table(UnitValue.createPercentArray(numImagesPerLine)).useAllAvailableWidth();
            for (int i = imageRIndex; i < numImages; i++) {
                Cell c = new Cell();
                c.setBorder(Border.NO_BORDER);
                c.setVerticalAlignment(VerticalAlignment.MIDDLE);
                c.setHorizontalAlignment(HorizontalAlignment.CENTER);

                Image image = createPdfImage(readReportImageData(images.get(i)), fixedImageWidth, fixedImageHeight);
                image.setHorizontalAlignment(HorizontalAlignment.CENTER);
                c.add(image);

                nestTable.addCell(c);
            }
            float marLeft = pageWidth - pageLpad - pageRpad
                    - numImagesPerLine * fixedImageWidth;
            marLeft /= 2;
            nestTable.setMarginLeft(marLeft);
            nestTable.setBorder(Border.NO_BORDER);
            cell.add(nestTable);
        }
        table.addCell(cell);
        if (log.isDebugEnabled()) {
            log.debug("输出pdf 图像ok.");
        }                //
        float bodyLineHeight = 1.1f;
        //
//        cell = new Cell();
//        cell.setBorder(Border.NO_BORDER);

        Paragraph para = null;
        //检查所见
//                para = new Paragraph("检查所见：").setFontSize(bodyHeadFontSize);
//                para.setMarginTop(2f);
//                para.setMarginBottom(2f);
//                cell.add(para);
//                String examDesc = report.getExamDesc();
                /*if(StringUtils.isNotBlank(examDesc)) {
                    String[] lines = examDesc.split("[\n]");
                    for(String line : lines) {
                        para = new Paragraph(line).setFontSize(bodyFontSize);
                        //para.setMarginTop(1.4f);
                        //para.setMarginBottom(1.6f);
                        para.setMarginLeft(bodyFontSize);
                        cell.add(para);
                    }
                }*/
//                para = new Paragraph(null != examDesc? examDesc : Const.SYMBOL_NL).setFontSize(bodyFontSize);
//                para.setMarginLeft(bodyFontSize);
//                para.setMultipliedLeading(bodyLineHeight);
//                cell.add(para);

        //检查诊断
        if (examModality.getDictValue().equals("TCD")) {
            if (examItem.getDictValue().equals("TCD-fpsy")) {
                para = new Paragraph(PdfUtil.boldText("发泡试验：")).setFontSize(bodyHeadFontSize);
                para.setMarginTop(2f);
                para.setMarginBottom(2f);
                cell.add(para);
            }
            para = new Paragraph(PdfUtil.boldText("印象：")).setFontSize(bodyHeadFontSize);
            para.setMarginTop(2f);
            para.setMarginBottom(2f);
            cell.add(para);
            String examDiag = report.getExamDesc();
            if (null != examDiag) {
                String[] examDiagArr = examDiag.split("\n");
                for (String str : examDiagArr) {
                    para = new Paragraph(str).setFontSize(bodyFontSize);
                    para.setFirstLineIndent(22);
                    para.setMultipliedLeading(bodyLineHeight);
                    cell.add(para);
                }
            }
//                    para = new Paragraph(null != examDiag ? examDiag : Const.SYMBOL_NL).setFontSize(bodyFontSize);
//                    para.setFirstLineIndent(20);
//                    para.setMultipliedLeading(bodyLineHeight);
//                    cell.add(para);

            //术后医嘱/建议
            para = new Paragraph(PdfUtil.boldText("提示：")).setFontSize(PdfUtil.bodyHeadFontSize);
            para.setMarginTop(2f);

            para.setMarginBottom(2f);
            cell.add(para);
            String operSugg = report.getExamDiagnosis();

            if (null != operSugg) {
                String[] examDiagArr = operSugg.split("\n");
                for (String str : examDiagArr) {
                    para = new Paragraph(str).setFontSize(PdfUtil.bodyFontSize);
                    para.setFirstLineIndent(22);
                    para.setMultipliedLeading(bodyLineHeight);
                    cell.add(para);
                }
            }

//                    para = new Paragraph(null != operSugg ? operSugg : Const.SYMBOL_NL).setFontSize(PdfUtil.bodyFontSize);
//                    para.setMarginLeft(PdfUtil.bodyFontSize);
//                    para.setMultipliedLeading(bodyLineHeight);
//                    cell.add(para);
            //
            table.addCell(cell);
        } else if (examModality.getDictValue().equals("DS")) {
            //检查所见
            para = new Paragraph("检查所见：").setFontSize(bodyHeadFontSize);
            para.setMarginTop(2f);
            para.setMarginBottom(2f);
            cell.add(para);
            String examDesc = report.getExamDesc();

            para = new Paragraph(null != examDesc ? examDesc : Const.SYMBOL_NL).setFontSize(bodyFontSize);
            para.setMarginLeft(bodyFontSize);
            para.setMultipliedLeading(bodyLineHeight);
            cell.add(para);
            table.addCell(cell);
        }
//        else {
//            //检查所见
//            para = new Paragraph(PdfUtil.boldText("检查所见：")).setFontSize(bodyHeadFontSize);
//            para.setMarginTop(2f);
//            para.setMarginBottom(2f);
//            cell.add(para);
//            String examDesc = report.getExamDesc();
//
//            para = new Paragraph(null != examDesc? examDesc : Const.SYMBOL_NL).setFontSize(bodyFontSize);
//            para.setMarginLeft(bodyFontSize);
//            para.setMultipliedLeading(bodyLineHeight);
//            cell.add(para);
//
//            //检查诊断
//            para = new Paragraph(PdfUtil.boldText("检查诊断：")).setFontSize(bodyHeadFontSize);
//            para.setMarginTop(2f);
//            para.setMarginBottom(2f);
//            cell.add(para);
//            String examDiag = report.getExamDiagnosis();
//            para = new Paragraph(null != examDiag? examDiag : Const.SYMBOL_NL).setFontSize(bodyFontSize);
//            para.setMarginLeft(bodyFontSize);
//            para.setMultipliedLeading(bodyLineHeight);
//            cell.add(para);
//
//            table.addCell(cell);
//        }
        return table;
    }

    //获取document占用页数
    public int getPageNum(Document document, ExamInfo report, int mainPos, float pageWidth, float pageLpad, float pageRpad) throws Exception {
        document.add(getTable(report, mainPos, pageWidth, pageLpad, pageRpad));
        return document.getPdfDocument().getNumberOfPages();
    }

    //获取检查部位para
    public Paragraph getParagraph(Rectangle area, String rpl, int pPos, int firstLineIndent, int lineHeight, float pageLpad) throws Exception {
        Paragraph para = new Paragraph(rpl).setFontSize(PdfUtil.metaFontSize)
                .setMargin(0f).setPadding(0f);
        para.setFont(PdfUtil.createFont())
                .setFixedLeading(lineHeight)
                .setFixedPosition(pageLpad, area.getBottom() - pPos, area.getWidth())
                .setFirstLineIndent(firstLineIndent);
        return para;
    }

    public JSONObject getPicPaObj(JSONObject picObj, String objKey, String key, String picBase64) {

        JSONObject value = new JSONObject();
        value.put(key, picBase64);
        picObj.put(objKey, value);
        return picObj;
    }

    /**
     * 获取报告pdf
     * @param report
     * @return
     * @throws Exception
     */
    public byte[] getReportPdf(ExamInfo report) throws Exception {
        //耳鼻喉 睡眠监测
        String reportUrl = report.getReportUrlOrgPdf();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        if(StringUtils.isBlank(reportUrl)) {
//            throw new RuntimeException("该检查没有生成报告文档。");
            return new byte[0];
        }
        reportUrl = yyy.xxx.common.net.storage.utils.WebUtil.putUserinfo(reportUrl, report.getReportUrlUsername(), report.getReportUrlPassword());
        StorageInterface<?, ?> storage = StorageInterfaceFactory.getStorage(reportUrl, Const.STORAGE_SESSION_OPTION);
        try {
            storage.read(reportUrl, outputStream);
            byte[] outB =  outputStream.toByteArray();
            return outB;
        } catch (Exception err) {
            log.error(err.getMessage(),err);
            return new byte[0];
        } finally {
            storage.release();
        }
    }

    /*public static String ImageToBase64(InputStream imgPath) {
        byte[] data = null;
        // 读取图片字节数组
        try {
            data = new byte[imgPath.available()];
            imgPath.read(data);
        } catch (IOException e) {
            e.printStackTrace();
        }

        Base64.Encoder encoder = Base64.getEncoder();
        return encoder.encodeToString(data);
    }*/

    /*public String getReportBase64(ExamInfo report) throws Exception {
        //耳鼻喉 睡眠监测
        String reportUrl = report.getReportUrlOrgPdf();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        if (StringUtils.isBlank(reportUrl)) {
            throw new RuntimeException("该检查没有生成报告文档。");
        }
        reportUrl = yyy.xxx.common.net.storage.utils.WebUtil.putUserinfo(reportUrl, report.getReportUrlUsername(), report.getReportUrlPassword());
        StorageInterface<?, ?> storage = StorageInterfaceFactory.getStorage(reportUrl, Const.STORAGE_SESSION_OPTION);
        try {
            storage.read(reportUrl, outputStream);
            byte[] outB = outputStream.toByteArray();

            String pdfData = ImageUtils.imageToBase64((new ByteArrayInputStream(outB)));
            return pdfData;
        } catch (Exception err) {
            throw err;
        } finally {
            storage.release();
        }
    }

    //只是签名
    public String test(String url, String modelPath, ExamInfo examInfo) throws Exception {
        //String url = "http://localhost:8080/design/api/preview/report/render/req";//***********
        JSONObject body = new JSONObject();

        JSONObject awaTime = new JSONObject();
        awaTime.put("awaitTimeSecond", 7);
        body.put("extra", awaTime);
        body.put("id", examInfo.getId());
        body.put("path", modelPath);

        JSONObject picObj = new JSONObject();
//        picObj = getPicPaObj(picObj,"report","src",getReportBase64(examInfo));
        picObj = getPicPaObj(picObj, "signature", "src", "data:image/png;base64," + examInfo.getSignImage());
        body.put("value", picObj);
        String bodyString = body.toString();
        String resData = httpClient.ajaxPost(url, bodyString, Const.CHARSET_UTF8);
        JSONObject resObj = JSONObject.parseObject(resData);
        JSONObject resCode1 = resObj.getJSONObject("value");
        JSONObject resCode2 = resCode1.getJSONObject("value");
        String img = resCode2.getString("img");
        return img;
    }*/

    //结构化报告签名
    public String writeAndSave(ExamInfo examInfo, String reportImgBase64, String SignImageBase64) {
        String reportDesignValue = getSysConfig("reportDesignUrlTemp");
        JSONObject repDeVJs = JSONObject.parseObject(reportDesignValue);
        String url = repDeVJs.getString("apiUrl");

        JSONObject mExam = repDeVJs.getJSONObject(examInfo.getExamItem().getDictValue());
        if (null == mExam) return null;
        String modelPath = mExam.getString("structTemplate");

//        if(0==report.getReportDesignImg().size()) {
//            throw new RuntimeException("该检查没有结构化报告图片");
//        }
//        String base64String = report.getReportDesignImg().get(0);

        //base64String = test1(url,repDeVJs.getString(report.getExamItem().getDictValue()),report);

        //String url = "http://localhost:8080/design/api/preview/report/render/req";//http://*************:8080/#/
        JSONObject body = new JSONObject();

        JSONObject awaTime = new JSONObject();
        awaTime.put("awaitTimeSecond", 7);
        body.put("extra", awaTime);
        body.put("id", examInfo.getId());
        body.put("path", modelPath);
        JSONObject picObj = new JSONObject();
        if (null != reportImgBase64)
            picObj = getPicPaObj(picObj, "report", "src", "data:image/png;base64," + reportImgBase64);
        if (null != SignImageBase64)
            picObj = getPicPaObj(picObj, "auditDoctorSign", "src", "data:image/png;base64," + SignImageBase64);
//        if(null!=examInfo.getAuditTime()){
//            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");
//            String formattedDate = dateFormat.format(examInfo.getAuditTime());
//            picObj = getPicPaObj(picObj,"signatureDate","text",formattedDate);
//        }

        body.put("value", picObj);
        String bodyString = body.toString();
        if (log.isDebugEnabled()) {
            log.debug("请求结构化报告，url:{},id:{},path:{}", url, examInfo.getId(), modelPath);
        }
        String resData = httpClient.ajaxPost(url, bodyString, Const.CHARSET_UTF8);
        if (log.isDebugEnabled()) {
            log.debug("请求结构化报告返回，url:{},id:{},path:{}", url, examInfo.getId(), modelPath);
        }
        JSONObject resObj = JSONObject.parseObject(resData);
        JSONObject resCode1 = resObj.getJSONObject("value");
        JSONObject resCode2 = resCode1.getJSONObject("value");
        String img = resCode2.getString("img");
        return img;
    }

    //结构化报告签名
    public String getDesignImg2(ExamInfo examInfo) {
        String reportDesignValue = getSysConfig("reportDesignUrlTemp");
        JSONObject repDeVJs = JSONObject.parseObject(reportDesignValue);
        String url = repDeVJs.getString("apiUrl");

        JSONObject mExam = repDeVJs.getJSONObject(examInfo.getExamItem().getDictValue());
        if (null == mExam) return null;
        String modelPath = mExam.getString("structTemplate");

//        if(0==report.getReportDesignImg().size()) {
//            throw new RuntimeException("该检查没有结构化报告图片");
//        }
//        String base64String = report.getReportDesignImg().get(0);

        //base64String = test1(url,repDeVJs.getString(report.getExamItem().getDictValue()),report);

        //String url = "http://localhost:8080/design/api/preview/report/render/req";//http://*************:8080/#/
        JSONObject body = new JSONObject();

        JSONObject awaTime = new JSONObject();
        awaTime.put("awaitTimeSecond", 7);
        body.put("extra", awaTime);
        body.put("id", examInfo.getId());
        body.put("path", modelPath);
//        JSONObject picObj = new JSONObject();
//        if(null!=reportImgBase64)picObj = getPicPaObj(picObj,"report","src","data:image/png;base64,"+reportImgBase64);
//        if(null!=SignImageBase64)picObj = getPicPaObj(picObj,"auditDoctorSign","src","data:image/png;base64,"+SignImageBase64);
////        if(null!=examInfo.getAuditTime()){
////            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");
////            String formattedDate = dateFormat.format(examInfo.getAuditTime());
////            picObj = getPicPaObj(picObj,"signatureDate","text",formattedDate);
////        }
//
//        body.put("value",picObj);
        String bodyString = body.toString();
        if (log.isDebugEnabled()) {
            log.debug("请求结构化报告，url:{},id:{},path:{}", url, examInfo.getId(), modelPath);
        }
        String resData = httpClient.ajaxPost(url, bodyString, Const.CHARSET_UTF8);
        if (log.isDebugEnabled()) {
            log.debug("请求结构化报告返回，url:{},id:{},path:{}", url, examInfo.getId(), modelPath);
        }
        JSONObject resObj = JSONObject.parseObject(resData);
        JSONObject resCode1 = resObj.getJSONObject("value");
        JSONObject resCode2 = resCode1.getJSONObject("value");
        String img = resCode2.getString("img");
        return img;
    }

    //结构化报告签名
    public String getDesignImg(ExamInfo examInfo) {
        String reportDesignValue = getSysConfig("reportDesignTemplateConfig");
        JSONObject repDeVJs = JSONObject.parseObject(reportDesignValue);
        String url = repDeVJs.getString("getPngUrl");
        if (StringUtils.isBlank(url)) {
            log.error("请配置getPngUrl获取结构化图片路径");
            throw new IllegalArgumentException("请配置getPngUrl获取结构化图片路径");
        }


//        JSONObject mExam = repDeVJs.getJSONObject(examInfo.getExamItem().getDictValue());
//        if (null == mExam) return null;
//        String modelPath = mExam.getString("structTemplate");
        String modelPath = jsonConfigService.getStructTemplate(examInfo.getExamItem().getDictValue());

        url += "?path=" + modelPath + "&id=" + examInfo.getId();

        if (log.isDebugEnabled()) {
            log.debug("请求结构化报告，url:{},id:{},path:{}", url, examInfo.getId(), modelPath);
        }

        RestTemplate restTemplate = new RestTemplate();
        byte[] imageBytes = null;
        try {
            // 发送 GET 请求并获取 ResponseEntity，其中包含图片的字节内容
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.IMAGE_PNG);

            HttpEntity<byte[]> requestEntity = new HttpEntity<>(headers);
            ResponseEntity<byte[]> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    requestEntity,
                    byte[].class
            );

            if (response.getStatusCode().is2xxSuccessful()) {
                imageBytes = response.getBody();
            } else {
                log.error("获取结构化报告图片异常");
                throw new IllegalArgumentException("获取结构化报告图片异常");
            }
        } catch (Exception e) {
            log.error("获取结构化报告图片异常");
            throw new IllegalArgumentException("获取结构化报告图片异常");
        }

        if (null==imageBytes) {
            log.error("获取结构化报告图片异常");
            throw new IllegalArgumentException("获取结构化报告图片异常,请检查报告是否已经保存");
        }


        // 使用Base64编码字节数组
        String base64Encoded2 = Base64.getEncoder().encodeToString(imageBytes);

        return "data:image/png;base64," + base64Encoded2;
    }

    //结构化报告签名
    public String test1(String url, String modelPath, ExamInfo examInfo) {

        //String url = "http://localhost:8080/design/api/preview/report/render/req";//http://*************:8080/#/
        JSONObject body = new JSONObject();

        JSONObject awaTime = new JSONObject();
        awaTime.put("awaitTimeSecond", 7);
        body.put("extra", awaTime);
        body.put("id", examInfo.getId());
        body.put("path", modelPath);
        JSONObject picObj = new JSONObject();
        picObj = getPicPaObj(picObj, "signature", "src", "data:image/png;base64," + examInfo.getSignImage());
        if (null != examInfo.getAuditTime()) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");
            String formattedDate = dateFormat.format(examInfo.getAuditTime());
            picObj = getPicPaObj(picObj, "signatureDate", "text", formattedDate);
        }

        body.put("value", picObj);
        String bodyString = body.toString();
        if (log.isDebugEnabled()) {
            log.debug("请求结构化报告，url:{},id:{},path:{}", url, examInfo.getId(), modelPath);
        }
        String resData = httpClient.ajaxPost(url, bodyString, Const.CHARSET_UTF8);
        if (log.isDebugEnabled()) {
            log.debug("请求结构化报告返回，url:{},id:{},path:{}", url, examInfo.getId(), modelPath);
        }
        JSONObject resObj = JSONObject.parseObject(resData);
        JSONObject resCode1 = resObj.getJSONObject("value");
        JSONObject resCode2 = resCode1.getJSONObject("value");
        String img = resCode2.getString("img");
        return img;
    }

    public void createDoc2(OutputStream outStream, ExamInfo report, Boolean isInstant, String templateWay) throws Exception {
        if (null == report.getReportDesignImg() || 0 == report.getReportDesignImg().size()) {
            throw new RuntimeException("该检查没有结构化报告图片");
        }
        String base64String = report.getReportDesignImg().get(0);

        //base64String = test1(url,repDeVJs.getString(report.getExamItem().getDictValue()),report);


        //测试
//        byte[] imageBytes2 = Base64.getDecoder().decode(base64String.split(",")[1]);
//        // 创建一个文件输出流
//        FileOutputStream fileOutputStream = new FileOutputStream("D:\\temp\\report_g.png");
//        // 将字节数组写入文件
//        fileOutputStream.write(imageBytes2);
//        fileOutputStream.close();


        if (base64String.startsWith("data:image")) {
            String[] parts = base64String.split(",");
            if (parts.length > 1) {
                base64String = parts[1];
            }
        }

        // 将Base64字符串解码为字节数组
        byte[] imageBytes = Base64.getDecoder().decode(base64String);
        PdfDocument pdfDocument = null;
        float pageWidth = 590, pageHeight = 842;
        pageHeight = -1;
        if (templateWay.equals("EndOfStructTemplate")) {
            //结构化报告叠加在pdf尾部
            String reportUrl = FileUrlDesUtil.decode(report.getReportUrlOrgPdf());
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            if (StringUtils.isBlank(reportUrl)) {
                throw new RuntimeException("该检查没有生成报告文档。");
            }
            reportUrl = yyy.xxx.common.net.storage.utils.WebUtil.putUserinfo(reportUrl, report.getReportUrlUsername(), report.getReportUrlPassword());
            StorageInterface<?, ?> storage = StorageInterfaceFactory.getStorage(reportUrl, Const.STORAGE_SESSION_OPTION);
            try {
                storage.read(reportUrl, outputStream);
            } catch (Exception err) {

                throw err;
            } finally {
                storage.release();
            }
            InputStream inputStreamT = new ByteArrayInputStream(outputStream.toByteArray());
            PdfReader reader = new PdfReader(inputStreamT);
            PdfWriter writer = new PdfWriter(outStream);
            pdfDocument = new PdfDocument(reader, writer);
            PageSize pageSize = pdfDocument.getDefaultPageSize();
            pageWidth = pageSize.getWidth();
            pageHeight = pageSize.getHeight();
        }
//        else if(examItem.getDictValue().equals("NYST")){
//            SysDictData examModality = report.getExamModality(), inpType = report.getInpType();
//            String[] tplFilesName = new String[]{
//                    String.format("report-MOD=%s-INP=%s.pdf", examModality.getDictValue(), inpType.getDictValue())
//                    , String.format("report-MOD=%s-ITEM=%s.pdf", examModality.getDictValue(), examItem.getDictValue())
//                    , String.format("report-MOD=%s.pdf", examModality.getDictValue())
//                    , String.format("report-INP=%s.pdf", inpType.getDictValue())
//                    , "report.pdf"};
//            Path tplFileFold = FileUtils.getDir("resfiles/template");
//            File tplFile = null;// = .resolve("report.pdf").toFile();
//            for(int i = 0; i < tplFilesName.length; i ++) {
//                Path tplFile0 = tplFileFold.resolve(tplFilesName[i]);
//                if(Files.isRegularFile(tplFile0)) {
//                    tplFile = tplFile0.toFile();
//                    break;
//                }
//            }
//            if(log.isInfoEnabled()) { log.info("检查报告模板={}", tplFile); }
//            if(null == tplFile) { throw new FileNotFoundException("报告模板不存在。"); }
//            if(log.isDebugEnabled()) { log.debug("输出pdf start...."); }
//
//            String fontProgram=null;
//
//            if(isLinux()){
//                //linux 楷体
//                fontProgram = "/usr/share/fonts/simsun/simsun.ttc";
//            }else{
//                //windows 楷体
//                fontProgram = "C:/Windows/Fonts/simsun.ttc";
//            }
//
//            PdfReader reader = new PdfReader(tplFile);
//            PdfWriter writer = new PdfWriter(outStream);
//            pdfDocument = new PdfDocument(reader, writer);
//        }
        else if (templateWay.equals("OnlyStructTemplate")) {
            //只有结构化模板图片
            PdfWriter writer = new PdfWriter(outStream);
            pdfDocument = new PdfDocument(writer);
        }

        float pageLpad = 0, pageRpad = 0;
        int mainPos = 0;
        try (Document document = new Document(pdfDocument);) {
            if (templateWay.equals("EndOfStructTemplate")) {
                document.add(new AreaBreak(AreaBreakType.LAST_PAGE));
                document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));

                document.add(new Paragraph(""));
            }

//                    test1("/测试/后端渲染.rtpl",report);
            Table table = getJpgTable1(report, mainPos, pageWidth, pageHeight, pageLpad, pageRpad, 0, imageBytes);
            if (log.isDebugEnabled()) {
                log.debug("输出pdf 诊断ok.");
            }

            document.add(table);
        }

    }

    /**
     * 输出pdf
     *
     * @param outStream 输出
     * @param report    报告内容
     * @param isInstant 完全使用报告图像、签名
     * @throws Exception
     */
    public void
    createDoc(OutputStream outStream, ExamInfo report, Boolean isInstant, String templateWay) throws Exception {
        //报告模板：report-MOD={设备类型}-INP={就诊类型}.pdf > report-MOD={设备类型}.pdf > report-INP={就诊类型}.pdf > report.pdf
        SysDictData examModality = report.getExamModality(), inpType = report.getInpType(), examItem = report.getExamItem();
        String[] tplFilesName = new String[]{
                String.format("report-MOD=%s-INP=%s.pdf", examModality.getDictValue(), inpType.getDictValue())
                , String.format("report-MOD=%s-ITEM=%s.pdf", examModality.getDictValue(), examItem.getDictValue())
                , String.format("report-MOD=%s.pdf", examModality.getDictValue())
                , String.format("report-INP=%s.pdf", inpType.getDictValue())
                , "report.pdf"};
        Path tplFileFold = FileUtils.getDir("resfiles/template");
        File tplFile = null;// = .resolve("report.pdf").toFile();
        for (int i = 0; i < tplFilesName.length; i++) {
            Path tplFile0 = tplFileFold.resolve(tplFilesName[i]);
            if (Files.isRegularFile(tplFile0)) {
                tplFile = tplFile0.toFile();
                break;
            }
        }
        if (log.isInfoEnabled()) {
            log.info("检查报告模板={}", tplFile);
        }
        if (null == tplFile) {
            throw new FileNotFoundException("报告模板不存在。");
        }
        if (log.isDebugEnabled()) {
            log.debug("输出pdf start....");
        }

        String fontProgram = null;

        if (isLinux()) {
            //linux 楷体
            fontProgram = "/usr/share/fonts/simsun/simsun.ttc";
        } else {
            //windows 楷体
            fontProgram = "C:/Windows/Fonts/simsun.ttc";
        }

        PdfFont font;
        if (new File(fontProgram).isFile()) {
            //windows字体
            font = PdfFontFactory.createFont(fontProgram + ",1", PdfEncodings.IDENTITY_H);
        } else {
            //itext字体，宋体
            font = PdfFontFactory.createFont("STSong-Light", "UniGB-UCS2-H", PdfFontFactory.EmbeddingStrategy.PREFER_EMBEDDED);
        }
        if (log.isDebugEnabled()) {
            log.debug("输出pdf 字体ok.");
        }
        //文本颜色
        Color fontColor = new DeviceRgb(0, 0, 0);
        //字体大小
        int metaFontSize = 10, bodyHeadFontSize = metaFontSize + 2, bodyFontSize = metaFontSize + 1;

        PdfReader reader;
        if (examModality.getDictValue().equals("Otol") && examItem.getDictValue().equals("Otol_REM")) {
            //耳鼻喉 睡眠监测
            String reportUrl = FileUrlDesUtil.decode(report.getReportUrlOrgPdf());
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            if (StringUtils.isBlank(reportUrl)) {
                throw new RuntimeException("该检查没有生成报告文档。");
            }
            reportUrl = yyy.xxx.common.net.storage.utils.WebUtil.putUserinfo(reportUrl, report.getReportUrlUsername(), report.getReportUrlPassword());
            StorageInterface<?, ?> storage = StorageInterfaceFactory.getStorage(reportUrl, Const.STORAGE_SESSION_OPTION);
            try {
                storage.read(reportUrl, outputStream);
            } catch (Exception err) {

                throw err;
            } finally {
                storage.release();
            }
            InputStream inputStreamT = new ByteArrayInputStream(outputStream.toByteArray());
            reader = new PdfReader(inputStreamT);
        } else {
            reader = new PdfReader(tplFile);
        }


        //读取模板，输出报告
        try (PdfWriter writer = new PdfWriter(outStream); PdfDocument pdfDocument = new PdfDocument(reader, writer);) {
            if (log.isDebugEnabled()) {
                log.debug("输出pdf 获取占位....");
            }            //
            PageSize pageSize = pdfDocument.getDefaultPageSize();
            float pageHeightR = pdfDocument.getFirstPage().getPageSize().getHeight();
            float pageWidthR = pdfDocument.getFirstPage().getPageSize().getWidth();
            float pageWidth = pageSize.getWidth(), pageHeight = pageSize.getHeight(), pageLpad = 40, pageRpad = 18;
            //替换固定信息占位符
            String[] vars = new String[]{"\\{pi.nm\\}", "\\{pi.regNo\\}", "\\{pi.gd\\}"
                    , "\\{pi.ag\\}", "\\{inpNo\\}", "\\{examNo\\}", "\\{No\\}"
                    , "\\{examDt\\}", "\\{examTm\\}", "\\{examPrt\\}", "\\{bedNo\\}"
                    , "\\{rpDt\\}", "\\{reqd.nm\\}", "\\{adtd.sign\\}", "\\{operd.nm\\}", "\\{reqdp.nm\\}"};
            //用白色刷掉被替换文字
            Color cvbg = ColorConstants.WHITE;
            CompositeCleanupStrategy strategy = new CompositeCleanupStrategy();
            for (String var : vars) {
                strategy.add(new RegexBasedCleanupStrategy(var).setRedactionColor(cvbg));
            }

            PdfCleaner.autoSweepCleanUp(pdfDocument, strategy);
            if (log.isDebugEnabled()) {
                log.debug("输出pdf 填充内容.");
            }            //
            try (Document document = new Document(pdfDocument);) {

                //临时Document,用于计算报告页数
                PdfWriter writerTTemp = new PdfWriter(new ByteArrayOutputStream());
                PdfDocument pdfDocumentTemp = new PdfDocument(writerTTemp);
                Document documentTemp = new Document(pdfDocumentTemp);
                documentTemp.setFont(PdfUtil.createFont()).setFontColor(PdfUtil.fontColor);

                //文字
                document.setFont(font).setFontColor(fontColor);
                //当前内容顶部距离
                int mainPos = 100;
                //患者信息
                Patient pat = report.getPatientInfo();
                Date examTime = report.getExamTime();
                if (null == examTime && null != report.getReportTime()) {
                    examTime = report.getReportTime();
                }

                if (examModality.getDictValue().equals("BC")) {
                    Table table = getJpgTable(report, mainPos, pageWidth, pageHeight, pageLpad, pageRpad, 0);
                    Table table1 = getJpgTable(report, mainPos, pageWidth, pageHeight, pageLpad, pageRpad, 1);
                    if (log.isDebugEnabled()) {
                        log.debug("输出pdf 诊断ok.");
                    }

                    //
                    document.add(table);
                    document.add(table1);
                } else if (examModality.getDictValue().equals("INF") || examItem.getDictValue().equals("Otol_PTA")) {
                    Table table = getJpgTable(report, mainPos, pageWidth, pageHeight, pageLpad, pageRpad, 0);

                    if (log.isDebugEnabled()) {
                        log.debug("输出pdf 诊断ok.");
                    }

                    //
                    document.add(table);
                } else if (examItem.getDictValue().equals("TCD-I") || examItem.getDictValue().equals("TCD-st") || examItem.getDictValue().equals("TCD-fp") || examItem.getDictValue().equals("TCD-pt")) {
                    for (int i = 0; i < report.getImages().size(); i++) {
                        Table table = getJpgTable(report, mainPos, pageWidth, pageHeight, pageLpad, pageRpad, i);
                        document.add(table);
                    }


                    if (log.isDebugEnabled()) {
                        log.debug("输出pdf 诊断ok.");
                    }
                } else if (examModality.getDictValue().equals("Otol") && examItem.getDictValue().equals("Otol_REM")) {

//                    for(int i=0;i<pageN;i++) {
                    document.add(new AreaBreak(AreaBreakType.LAST_PAGE));
                    document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
//                    }

                    document.add(new Paragraph(""));
                    Table table = getJpgTable(report, mainPos, pageWidth, pageHeight, pageLpad, pageRpad, 0);

                    if (log.isDebugEnabled()) {
                        log.debug("输出pdf 诊断ok.");
                    }

                    //
                    document.add(table);
                    //int aa = report.getAuditTime().getYear();
                    if (null != report.getAuditTime())
                        document.add(new Paragraph("日期：" + report.getAuditTime().getYear() + '-' + (report.getAuditTime().getMonth() + 1) + '-' + report.getAuditTime().getDate()));

                    for (int i = 1; i < pdfDocument.getNumberOfPages() + 1; i++) {
                        int pageNum = i;
                        PdfPage page = pdfDocument.getLastPage();
                        PdfCanvas pdfCanvas = new PdfCanvas(page.newContentStreamAfter(), page.getResources(), page.getDocument());
                        Rectangle area = new Rectangle(256, 256);
                        area.setWidth(256);
                        area.setY(area.getY() + 1);
                        try (Canvas canvas = new Canvas(pdfCanvas, area);) {

                            String rpl = null;
                            //签名图片
                            String signImageData = report.getSignImage();
                            //补发，附件取签名
                            if (!isInstant && StringUtils.isBlank(signImageData)) {
                                readSignImage(report);
                                signImageData = report.getSignImage();
                            }
                            //
                            if (StringUtils.isNotBlank(signImageData)) {
                                int fixedHeight = 26;
                                float x = pageWidthR - 160;
                                float y = 20;
                                if (report.getExamItem().getDictValue().equals("HSN")) {
                                    x = 150;
                                    y = 120;
                                }

//                                Paragraph para = new Paragraph("日期："+ 2024 +'-' +(report.getAuditTime().getMonth()+1)+'-'+ report.getAuditTime().getDate()).setFontSize(metaFontSize)
//                                        .setMargin(0f).setPadding(0f);
//
//                                Rectangle areaT = new Rectangle(256, 256);
//                                areaT.setWidth(256);
//                                areaT.setY(area.getY() - 500);
//                                areaT.setX(areaT.getX() - 45);
//                                Canvas canvasT = new Canvas(pdfCanvas, areaT);
//                                canvasT.setFont(font).setFontColor(fontColor);
//                                canvasT.add(para);

                                Image image = createPdfImage(yyy.xxx.simpfw.common.utils.StringUtils.base64ToBytes(signImageData), 45, 24)
                                        .setFixedPosition(pageNum, x, y);
                                canvas.add(image);

                            } else if (null != report.getAuditDoctor()) {
                                rpl = report.getAuditDoctor().getNickName();
                            }

                            if (null != rpl) {
                                Paragraph para = new Paragraph(rpl).setFontSize(10)
                                        .setMargin(0f).setPadding(0f);
                                canvas.add(para);//
                            }
                        }
                    }

                }

                //替换固定信息
                for (int i = 0; i < pdfDocument.getNumberOfPages(); i++) {
                    final int pageNum = 1 + i;
                    PdfPage page = pdfDocument.getPage(pageNum);
                    PdfCanvas pdfCanvas = new PdfCanvas(page.newContentStreamAfter(), page.getResources(), page.getDocument());
                    for (IPdfTextLocation location : strategy.getResultantLocations()) {
                        Rectangle area = location.getRectangle();
                        area.setWidth(256);
                        area.setY(area.getY() + 1);
                        try (Canvas canvas = new Canvas(pdfCanvas, area);) {
                            boolean isExamPart = false;
                            //替换内容
                            String var = location.getText(), rpl;
                            if ("{pi.nm}".equals(var)) {            //患者姓名
                                rpl = pat.getName();
                            } else if ("{pi.regNo}".equals(var)) {  //登记号
                                rpl = report.getPatientInfo().getRegistNo();
                            } else if ("{pi.gd}".equals(var)) {     //性别
                                rpl = null != pat.getGender() ? pat.getGender().getDictLabel() : null;
                            } else if ("{pi.ag}".equals(var)) {     //年龄
                                rpl = pat.readage();
                            } else if ("{inpNo}".equals(var)) {     //住院号
                                rpl = report.getInpNo();
                            } else if ("{examNo}".equals(var)) {    //检查号
                                rpl = report.getExamNo();
                            } else if ("{reqdp.nm}".equals(var)) {  //申请科室
                                rpl = null != report.getReqDept() ? report.getReqDept().getDeptName() : null;
                            } else if ("{No}".equals(var)) {  //申请科室
                                rpl = null != report.getImageNo() ? report.getImageNo() : report.getReportNo();
                            } else if ("{examDt}".equals(var)) {    //检查日期
                                rpl = null != examTime ? DateUtils.dateTime(examTime) : Const.EMPTY;
                            } else if ("{examTm}".equals(var)) {    //检查时间
                                rpl = null != examTime ? DateUtils.parseDateToStr(DateUtils.HH_MM_SS, examTime) : Const.EMPTY;
                            } else if ("{examPrt}".equals(var)) {   //检查部位
                                //换行处理
                                isExamPart = true;
                                final float width;// = pageWidth - pageLpad - pageRpad;//490;//pageWidth
                                if (ExamEnum.InpTypeBodyExam.equals(inpType.getDictValue())) {
                                    width = pageWidth - pageLpad - pageRpad;
                                } else {
                                    //默认模板
                                    width = pageWidth - pageLpad - pageRpad - 184;
                                }
                                area.setWidth(width);

                                rpl = StringUtils.join(null == report.getExamParts() ? "" : report.getExamParts().stream().map(ExamParts::getPartsName).collect(Collectors.toList()), ",");
                            } else if ("{bedNo}".equals(var)) {     //床号
                                rpl = null != report.getBedNo() ? report.getBedNo() : null;
                            } else if ("{rpDt}".equals(var)) {      //报告时间
                                rpl = null != report.getReportTime() ? DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, report.getReportTime()) : Const.EMPTY;
                            } else if ("{reqd.nm}".equals(var)) {   //检查医生
                                //rpl = null != report.getReqDoctor()? report.getReqDoctor().getNickName() : null;
                                rpl = null != report.getExamDoctorsName() ? report.getExamDoctorsName() : "";
                            } else if ("{operd.nm}".equals(var)) {   //操作医生
                                //rpl = null != report.getReqDoctor()? report.getReqDoctor().getNickName() : null;
                                rpl = null != report.getOperDoctor() ? report.getOperDoctor().getNickName() : "";
                            } else if ("{adtd.sign}".equals(var)) { //签名图片
                                //签名图片
                                String signImageData = report.getSignImage();
                                //补发，附件取签名
                                if (!isInstant && StringUtils.isBlank(signImageData)) {
                                    readSignImage(report);
                                    signImageData = report.getSignImage();
                                }
                                //
                                if (StringUtils.isNotBlank(signImageData)) {
                                    int fixedHeight = 26;
                                    float x = area.getX();
                                    float y = area.getTop() - fixedHeight;
//                                    if(examItem.getDictValue().equals("TCD-I")){
//                                        x = area.getX() + 250;
//                                        y = area.getY() - 10;
//                                    }
                                    Image image = createPdfImage(yyy.xxx.simpfw.common.utils.StringUtils.base64ToBytes(signImageData), 45, 24)
                                            .setFixedPosition(pageNum, x, y);
                                    canvas.add(image);

                                    if (examModality.getDictValue().equals("INF")) {
                                        Paragraph para = new Paragraph("操作者：").setFontSize(metaFontSize)
                                                .setMargin(0f).setPadding(0f);

                                        Rectangle areaT = location.getRectangle();
                                        areaT.setWidth(256);
                                        areaT.setY(area.getY() - 10);
                                        areaT.setX(areaT.getX() - 45);
                                        Canvas canvasT = new Canvas(pdfCanvas, areaT);
                                        canvasT.setFont(font).setFontColor(fontColor);
                                        canvasT.add(para);
                                    }

                                    if (examItem.getDictValue().equals("TCD-fp") || examItem.getDictValue().equals("TCD-st") || examItem.getDictValue().equals("TCD-pt")) {
                                        Paragraph para = new Paragraph("审核时间：" + DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, report.getAuditTime())).setFontSize(10)
                                                .setMargin(0f).setPadding(0f);

                                        Rectangle areaT = new Rectangle(256, 256);
                                        areaT.setWidth(256);

                                        areaT.setY(y - 255);
                                        areaT.setX(x - 30);
                                        Canvas canvasT = new Canvas(pdfCanvas, areaT);
                                        canvasT.setFont(font).setFontColor(new DeviceRgb(0, 0, 0));
                                        canvasT.add(para);
                                    }

                                    continue;
                                } else if (null != report.getAuditDoctor()) {
                                    rpl = report.getAuditDoctor().getNickName();
                                } else {
                                    continue;
                                }
                            } else {
                                continue;
                            }
                            if (null != rpl) {
                                Paragraph para = new Paragraph(rpl).setFontSize(metaFontSize)
                                        .setMargin(0f).setPadding(0f);
                                //检查部位换行处理
                                if (isExamPart && (rpl.length() * metaFontSize) > area.getWidth()) {
                                    int firstLineIndent = 5 * metaFontSize, numLines = 0;
                                    String[] segm = rpl.split("[\r\n]+");
                                    for (String l : segm) {
                                        while (l.length() * metaFontSize > area.getWidth()) {
                                            //首行缩进 5 * metaFontSize
                                            int indent = numLines == 0 ? firstLineIndent : 0;
                                            int pos = (int) Math.floor((area.getWidth() - indent) / metaFontSize);
                                            ++numLines;
                                            String sl = yyy.xxx.simpfw.common.utils.StringUtils.substr(l, pos * 2, Const.CHARSET_ZH);
                                            l = l.substring(sl.length());
                                        }
                                        if (!l.isEmpty()) {
                                            ++numLines;
                                        }
                                    }

                                    int lineHeight = metaFontSize, pPos = (numLines - 1) * lineHeight;
                                    //重画部位下横线
                                    PdfCanvas ln = new PdfCanvas(page);
                                    ln.setLineWidth(1.5F);
                                    ln.moveTo(pageLpad, area.getBottom() - pPos - 4).lineTo(pageWidth - pageRpad, area.getBottom() - pPos - 4);
                                    ln.closePathStroke();
                                    //擦掉部位下横线
                                    ln.setLineWidth(4F);
                                    ln.setStrokeColor(new DeviceRgb(255, 255, 255));
                                    ln.moveTo(0, area.getBottom() - 5).lineTo(pageWidth, area.getBottom() - 5);
                                    ln.closePathStroke();
                                    //
                                    //                                para.setFont(font)
                                    //                                        .setFixedLeading(lineHeight)
                                    //                                        .setFixedPosition(pageLpad, area.getBottom() - pPos, area.getWidth())
                                    //                                        .setFirstLineIndent(firstLineIndent);
                                    document.add(getParagraph(area, rpl, pPos, firstLineIndent, lineHeight, pageLpad));
                                    documentTemp.add(getParagraph(area, rpl, pPos, firstLineIndent, lineHeight, pageLpad));
                                    //
                                    mainPos += pPos;
                                    //
                                    continue;
                                }
                                //文本
                                canvas.setFont(font).setFontColor(fontColor);
                                canvas.add(para);//
                            }
                        }
                    }

                    JSONObject resultStatusC = JSON.parseObject(configService.selectConfigByKey("interfaceConfig"));
                    boolean reportStatus = resultStatusC.getBoolean("AddQRCode");
                    if (1 == pageNum && reportStatus) {
                        float qrImageWidth = 70;
                        float qrPosX = pageWidthR - 110;
                        float qrPosY = pageHeightR - 80;

                        Rectangle area = new Rectangle(256, 256);
                        area.setWidth(256);
                        area.setY(area.getY() + 1);
//                        area.setY(area.getY() + 1);
                        try (Canvas canvas = new Canvas(pdfCanvas, area);) {

                            String reportStr = JSONObject.toJSONString(report);
                            CCCExamInfo cccExamInfo = JSONObject.parseObject(reportStr, CCCExamInfo.class);
                            SysDictData modality = dictConvertToCCC.getModality(report);
                            cccExamInfo.setExamModality(modality);

                            String medicalTechCode = dictConvertToCCC.getMedicalTechCode(report);
                            cccExamInfo.setMedicalTechCode(medicalTechCode);

                            String dataSource = dictConvertToCCC.getDataSource(report);
                            String qrCodeImage = reportQRCodeService.getQRImage(cccExamInfo, dataSource);
                            //
                            if (StringUtils.isNotBlank(qrCodeImage)) {


                                Image image = createPdfImage(yyy.xxx.simpfw.common.utils.StringUtils.base64ToBytes(qrCodeImage), qrImageWidth, -1)
                                        .setFixedPosition(pageNum, qrPosX, qrPosY);
                                canvas.add(image);

                            }

                            int qrFontSize = 9;
                            float qrMar = -0.5f;
                            Paragraph para = new Paragraph("微信").setFontSize(qrFontSize)
                                    .setMargin(qrMar).setPadding(0f);
                            ;
                            Paragraph para2 = new Paragraph("扫码").setFontSize(qrFontSize)
                                    .setMargin(qrMar).setPadding(0f);
                            Paragraph para3 = new Paragraph("查看").setFontSize(qrFontSize)
                                    .setMargin(qrMar).setPadding(0f);
                            Paragraph para4 = new Paragraph("影像").setFontSize(qrFontSize)
                                    .setMargin(qrMar).setPadding(0f);
                            Paragraph para5 = new Paragraph("报告").setFontSize(qrFontSize)
                                    .setMargin(qrMar).setPadding(0f);

                            float areaW = 256;
                            Rectangle areaT = new Rectangle(areaW, areaW);
                            areaT.setWidth(areaW);
                            areaT.setX(qrPosX + qrImageWidth + 5);
                            areaT.setY(qrPosY - areaW + qrImageWidth);
                            Canvas canvasT = new Canvas(pdfCanvas, areaT);
                            canvasT.setFont(font).setFontColor(fontColor);
                            canvasT.add(para);
                            canvasT.add(para2);
                            canvasT.add(para3);
                            canvasT.add(para4);
                            canvasT.add(para5);

//                            Paragraph para = new Paragraph("微信：").setFontSize(metaFontSize)
//                                    .setMargin(0f).setPadding(0f);
//                            Paragraph para2 = new Paragraph("扫码：").setFontSize(metaFontSize)
//                                    .setMargin(0f).setPadding(0f);
//
//                            Rectangle areaT = new Rectangle(256, 256);
//                            areaT.setWidth(256);
//                            areaT.setY(50);
//                            areaT.setX(50);
//
//                            canvas.add(para);
//                            canvas.add(para2);
                        }

                    }
                }
                if (log.isDebugEnabled()) {
                    log.debug("输出pdf 主内容....");
                }
                if (!examModality.getDictValue().equals("BC") && !examModality.getDictValue().equals("INF") && !examItem.getDictValue().equals("TCD-I") && !examItem.getDictValue().equals("TCD-st") && !examItem.getDictValue().equals("TCD-fp") && !examItem.getDictValue().equals("TCD-pt") && !examItem.getDictValue().equals("Otol_REM")) {
                    Table table = getTable(report, mainPos, pageWidth, pageLpad, pageRpad);

                    if (log.isDebugEnabled()) {
                        log.debug("输出pdf 诊断ok.");
                    }

                    //
                    document.add(table);


                    //获取占用页数
                    int pageNum = getPageNum(documentTemp, report, mainPos, pageWidth, pageLpad, pageRpad);

                    //pdf页数
                    int pdfPageNum = document.getPdfDocument().getNumberOfPages();
                    //移除掉多余页
                    while (pdfPageNum > pageNum) {
                        document.getPdfDocument().removePage(pdfPageNum--);
                    }


                }

            }
        }
        if (log.isDebugEnabled()) {
            log.debug("输出pdf end.");
        }
    }

    /**
     * 获取签字图片
     */
    private SysUser getSignUser(LoginUser luser) {
        final String token = luser.getToken();
        //先从缓存获取
        final String cacheKey = CacheUtil.cacheKey("signUser");//"signatureImg"
        Map<String, SysUser> cache = redisCache.getCacheMap(cacheKey);
        SysUser signImg = null != cache ? cache.get(token) : null;
        if (null != signImg) {
            return signImg;
        }
        //缓存没有则调用接口
//        QRAuthStatus qrAuthStatus = null;
//        try {
//            qrAuthStatus = qrAuthSerice.fetchOauthStatus(token);
//        } catch (IllegalStateException err) {
//            //尝试常规账号关联的扫码账号
//            if(QRAuthConst.ERRM_NOAUTH.equals(err.getMessage())) {
//                String qrToken = qrAuthRelSerice.getRelation(token);
//                if(logger.isDebugEnabled()) { logger.debug("{}->{}", token, qrToken); }
//                if(StringUtils.isBlank(qrToken)) {
//                    throw new IllegalStateException(QRAuthConst.ERRM_NOAUTH);
//                }
//                qrAuthStatus = qrAuthSerice.fetchOauthStatus(qrToken);
//            }
//        }
//        if(null == qrAuthStatus) { return null; }

        signImg = new SysUser();
        signImg.setAvatar(luser.getUser().getAvatar());
        signImg.setUserName(luser.getUser().getUserName());
        signImg.setNickName(luser.getUser().getNickName());

        cache = null != cache ? cache : new HashMap<>();
        cache.put(token, signImg);
        redisCache.setCacheMap(cacheKey, cache);
        redisCache.expire(cacheKey, 1, TimeUnit.DAYS);

        return signImg;
    }

    @Transactional
    public int autoAudit(ExamInfo param, SysUser signUser) {
        //读取检查信息
        ExamInfo info = infoService.selectOne(param);
        if (null == info) {
            return -1;
        }
        //
//        LoginUser luser = SecurityUtils.getLoginUser();
//
//        //qr
//        SysUser signUser1;
//        try {
//            signUser1 = getSignUser(luser);
//        } catch (IllegalStateException err) {
//            return -1;
//        }

        //状态检查，书写完成->初审，审核->复审
        //SysDictData resultStatus = info.getResultStatus();
        SysDictData resultStatus = new SysDictData();
        resultStatus.setDictLabel("已报告");
        resultStatus.setDictValue("2");
        String resultStatusCode;
        if (null != resultStatus && StringUtils.isNotBlank((resultStatusCode = resultStatus.getDictValue()))) {
            //
            if (ResultStatus.REPORT.is(resultStatusCode)) {
                info.setAuditDoctor(signUser);
            } else if (ResultStatus.AUDIT.is(resultStatusCode)) {
                info.setReauditDoctor(signUser);
            } else {
                return -1;
            }
        } else {
            return -1;
        }
        //获取签字图片
        String signImg;
        try {
            signImg = null != signUser ? signUser.getAvatar() : null;
        } catch (IllegalStateException err) {
            return -1;
        }
        //当前数据库的工作状态
        info.setResultStatus(resultStatus);

        //param.setImages(info.getImages());
        //
        int resC = audit(info);
        //审核同时签字
        if (resC > 0 && ResultStatus.REPORT.is(resultStatusCode)) {
            //QRAuthConst.ERRM_NOAUTH
            //return sign(param);
            try {
                if (StringUtils.isNotEmpty(signImg)) {
                    SignInfo signInfo = new SignInfo();
                    signInfo.setUuid(signUser.getPassword());
                    info.setSignInfo(signInfo);
                }
                sign(info, signImg);
            } catch (Exception err) {
                //退回书写完成工作状态
                try {
                    resultStatus.setDictValue(ResultStatus.REPORT.getValue());
                    infoService.updateResultStatus(info);
                } catch (Exception er) {
                    log.error(err.getMessage(), err);
                }
                return -1;
            }
        }
        //生成报告后发送
        /*if(StringUtils.isNotBlank(info.getOrdId())) {
            reportInterService.sendReport(infoService.selectOne(param));
        }*/
        //
        return 0;
    }

    //给pdf贴签名图片
    public void signPdf(OutputStream outStream, ExamInfo report, Boolean isInstant, InputStream in) throws Exception {

        String fontProgram = null;

        if (isLinux()) {
            //linux 楷体
            fontProgram = "/usr/share/fonts/simsun/simsun.ttc";
        } else {
            //windows 楷体
            fontProgram = "C:/Windows/Fonts/simsun.ttc";
        }

        PdfFont font;
        if (new File(fontProgram).isFile()) {
            //windows字体
            font = PdfFontFactory.createFont(fontProgram + ",1", PdfEncodings.IDENTITY_H);
        } else {
            //itext字体，宋体
            font = PdfFontFactory.createFont("STSong-Light", "UniGB-UCS2-H", PdfFontFactory.EmbeddingStrategy.PREFER_EMBEDDED);
        }
        //文本颜色
        Color fontColor = new DeviceRgb(0, 0, 0);

        //读取模板，输出报告
        try (PdfReader reader = new PdfReader(in); PdfWriter writer = new PdfWriter(outStream); PdfDocument pdfDocument = new PdfDocument(reader, writer);) {
            float pageWidthR = pdfDocument.getFirstPage().getPageSize().getWidth();
            float pageHeightR = pdfDocument.getFirstPage().getPageSize().getHeight();
            if (log.isDebugEnabled()) {
                log.debug("输出pdf 填充内容.");
            }            //
            try (Document document = new Document(pdfDocument);) {
                for (int i = 1; i < pdfDocument.getNumberOfPages() + 1; i++) {
                    int pageNum = i;
                    PdfPage page = pdfDocument.getPage(pageNum);
                    PdfCanvas pdfCanvas = new PdfCanvas(page);
                    Rectangle area = new Rectangle(256, 256);
                    area.setWidth(256);
                    area.setY(area.getY() + 1);
                    try (Canvas canvas = new Canvas(pdfCanvas, area);) {

                        String rpl = null;
                        //签名图片
                        String signImageData = report.getSignImage();
                        //补发，附件取签名
                        if (!isInstant && StringUtils.isBlank(signImageData)) {
                            readSignImage(report);
                            signImageData = report.getSignImage();
                        }
                        //
                        if (StringUtils.isNotBlank(signImageData)) {
                            if (report.getExamItem().getDictValue().equals("EEG")) {
                                //脑电图

                            }  else if (report.getExamItem().getDictValue().equals("HSN")||report.getExamItem().getDictValue().equals("Vestibule")) {
                                int fixedHeight = 26;
                                float x = pageWidthR - 160;
                                float y = 20;
                                if (report.getExamItem().getDictValue().equals("HSN")) {
                                    x = 150;
                                    y = 120;
                                }
                                Image image = createPdfImage(yyy.xxx.simpfw.common.utils.StringUtils.base64ToBytes(signImageData), 45, 24)
                                        .setFixedPosition(pageNum, x, y);
                                canvas.add(image);
                            }
                            else if (report.getExamItem().getDictValue().equals("EMG")) {
                                if (i == pdfDocument.getNumberOfPages()) {
                                    int fixedHeight = 26;
                                    float x = pageWidthR - 160;
                                    float y = 30;

                                    Paragraph para = new Paragraph("操作者：" + (null != report.getOperDoctor() ? report.getOperDoctor().getNickName() : "")).setFontSize(10)
                                            .setMargin(0f).setPadding(0f);

                                    Rectangle areaT = new Rectangle(256, 256);
                                    areaT.setWidth(256);
                                    areaT.setY(y - 245);
                                    areaT.setX(x - 380);
                                    Canvas canvasT = new Canvas(pdfCanvas, areaT);
                                    canvasT.setFont(font).setFontColor(new DeviceRgb(0, 0, 0));
                                    canvasT.add(para);


                                    para = new Paragraph("审核医师：").setFontSize(10)
                                            .setMargin(0f).setPadding(0f);

                                    areaT = new Rectangle(256, 256);
                                    areaT.setWidth(256);
                                    areaT.setY(y - 245);
                                    areaT.setX(x - 130);
                                    canvasT = new Canvas(pdfCanvas, areaT);
                                    canvasT.setFont(font).setFontColor(new DeviceRgb(0, 0, 0));
                                    canvasT.add(para);

                                    Image image = createPdfImage(yyy.xxx.simpfw.common.utils.StringUtils.base64ToBytes(signImageData), 45, 24)
                                            .setFixedPosition(pageNum, x - 80, y - 5);
                                    canvas.add(image);

                                    para = new Paragraph("审核时间：" + DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, report.getAuditTime())).setFontSize(10)
                                            .setMargin(0f).setPadding(0f);

                                    areaT = new Rectangle(256, 256);
                                    areaT.setWidth(256);
                                    areaT.setY(y - 245);
                                    areaT.setX(x - 15);
                                    canvasT = new Canvas(pdfCanvas, areaT);
                                    canvasT.setFont(font).setFontColor(new DeviceRgb(0, 0, 0));
                                    canvasT.add(para);
                                }

                            } else {
                                int fixedHeight = 26;
                                float x = pageWidthR - 130;
                                float y = 45;
                                Image image = createPdfImage(yyy.xxx.simpfw.common.utils.StringUtils.base64ToBytes(signImageData), 45, 24)
                                        .setFixedPosition(pageNum, x, y);
                                canvas.add(image);

                                Paragraph para = new Paragraph("审核时间：" + DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, report.getAuditTime())).setFontSize(10)
                                        .setMargin(0f).setPadding(0f);

                                Rectangle areaT = new Rectangle(256, 256);
                                areaT.setWidth(256);
                                areaT.setY(y - 255);
                                areaT.setX(x - 15);
                                Canvas canvasT = new Canvas(pdfCanvas, areaT);
                                canvasT.setFont(font).setFontColor(new DeviceRgb(0, 0, 0));
                                canvasT.add(para);
                            }

                        } else if (null != report.getAuditDoctor()) {
                            rpl = report.getAuditDoctor().getNickName();
                        }

                        if (null != rpl) {
                            int metaFontSize = 10;
                            Paragraph para = new Paragraph(rpl).setFontSize(metaFontSize)
                                    .setMargin(0f).setPadding(0f);
                            canvas.add(para);//
                        }
                        boolean reportStatus = false;
                        try {
                            JSONObject resultStatusC = JSON.parseObject(configService.selectConfigByKey("interfaceConfig"));
                            reportStatus = resultStatusC.getBoolean("AddQRCode");
                        } catch (Exception err) {
                            log.error("请配置启用二维码AddQRCode标识");
                            throw new IllegalArgumentException("请配置启用二维码AddQRCode标识");
                        }

                        if (1 == pageNum && reportStatus) {

                            JSONObject qrCodeSizejs = JSON.parseObject(configService.selectConfigByKey("qrCodeSize"));
                            float qrPosX = qrCodeSizejs.getIntValue("x");
                            float qrPosY = qrCodeSizejs.getIntValue("y");
                            float qrImageWidth = qrCodeSizejs.getIntValue("w");
                            int qrFontSize = qrCodeSizejs.getIntValue("fontSize");

                            //添加二维码
//                            qrImageWidth = 70;
//                            qrPosX = pageWidthR - 110;
//                            qrPosY = pageHeightR - 80;


//                            String reportStr = JSONObject.toJSONString(report);
//                            CCCExamInfo cccExamInfo = JSONObject.parseObject(reportStr, CCCExamInfo.class);
//                            SysDictData modality = dictConvertToCCC.getModality(report);
//                            cccExamInfo.setExamModality(modality);
//
//                            String medicalTechCode = dictConvertToCCC.getMedicalTechCode(report);
//                            cccExamInfo.setMedicalTechCode(medicalTechCode);
//
//                            String dataSource = dictConvertToCCC.getDataSource(report);
//                            String qrCodeImage = reportQRCodeService.getQRImage(cccExamInfo, dataSource);
                            String qrCodeImage = dictConvertToCCC.getQRImage(report);

                            //
                            if (StringUtils.isNotBlank(qrCodeImage)) {

                                Image image = createPdfImage(yyy.xxx.simpfw.common.utils.StringUtils.base64ToBytes(qrCodeImage), qrImageWidth, -1)
                                        .setFixedPosition(pageNum, qrPosX, qrPosY);
                                canvas.add(image);

                            }

                            float qrMar = -0.5f;
                            Paragraph para1 = new Paragraph("微信").setFontSize(qrFontSize)
                                    .setMargin(qrMar).setPadding(0f);
                            ;
                            Paragraph para2 = new Paragraph("扫码").setFontSize(qrFontSize)
                                    .setMargin(qrMar).setPadding(0f);
                            Paragraph para3 = new Paragraph("查看").setFontSize(qrFontSize)
                                    .setMargin(qrMar).setPadding(0f);
                            Paragraph para4 = new Paragraph("影像").setFontSize(qrFontSize)
                                    .setMargin(qrMar).setPadding(0f);
                            Paragraph para5 = new Paragraph("报告").setFontSize(qrFontSize)
                                    .setMargin(qrMar).setPadding(0f);

                            float areaW = 256;
                            Rectangle areaT = new Rectangle(areaW, areaW);
                            areaT.setWidth(areaW);
                            areaT.setX(qrPosX + qrImageWidth + 5);
                            areaT.setY(qrPosY - areaW + qrImageWidth);
                            Canvas canvasT = new Canvas(pdfCanvas, areaT);
                            canvasT.setFont(font).setFontColor(fontColor);
                            canvasT.add(para1);
                            canvasT.add(para2);
                            canvasT.add(para3);
                            canvasT.add(para4);
                            canvasT.add(para5);

//                            Paragraph para = new Paragraph("微信：").setFontSize(metaFontSize)
//                                    .setMargin(0f).setPadding(0f);
//                            Paragraph para2 = new Paragraph("扫码：").setFontSize(metaFontSize)
//                                    .setMargin(0f).setPadding(0f);
//
//                            Rectangle areaT = new Rectangle(256, 256);
//                            areaT.setWidth(256);
//                            areaT.setY(50);
//                            areaT.setX(50);
//
//                            canvas.add(para);
//                            canvas.add(para2);
                        }
                    }
                }
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("输出pdf end.");
        }
    }


    @Transactional
    public int auditPdf(ExamInfo report, SysUser signUser) {

        String reportUrlOrgPdf = report.getReportUrlOrgPdf();
        if (StringUtils.isBlank(reportUrlOrgPdf)) {
            throw new RuntimeException("该检查没有生成报告文档。");
        }

        reportUrlOrgPdf = yyy.xxx.common.net.storage.utils.WebUtil.putUserinfo(reportUrlOrgPdf, report.getReportUrlUsername(), report.getReportUrlPassword());
        StorageInterface<?, ?> storage = StorageInterfaceFactory.getStorage(reportUrlOrgPdf, Const.STORAGE_SESSION_OPTION);
        try {
            //获取原始文件
            ByteArrayOutputStream outputStreamR = new ByteArrayOutputStream();
            storage.read(reportUrlOrgPdf, outputStreamR);

            //贴上签名图片
            InputStream inputStreamT = new ByteArrayInputStream(outputStreamR.toByteArray());
            ByteArrayOutputStream outputStreamSign = new ByteArrayOutputStream();
            signPdf(outputStreamSign, report, false, inputStreamT);

            //保存签名pdf
            InputStream inputStreamW = new ByteArrayInputStream(outputStreamSign.toByteArray());
            try {
//                String extName = FileUtils.getExtName(reportUrlOrgPdf);
                String fileName = reportUrlOrgPdf.substring(reportUrlOrgPdf.lastIndexOf('/') + 1);
                String newFileName = FileUtil.getReportAuditFileName(report,fileName);
                String newFilePath = reportUrlOrgPdf.replace(fileName, newFileName);

                //smb不能重写，同名文件改名
//                if (SmbStorage.handle(newFilePath) && storage.fileExists(newFilePath)) {
//
//                    String fileUrl0 = newFilePath.replace(Const.backslash, Const.slash);
//                    //截取路径//
//                    int pos = fileUrl0.indexOf(Const.slash2);
//                    //斜杠位置
//                    pos = fileUrl0.indexOf(Const.slash, pos + 2);
//                    pos = fileUrl0.indexOf(Const.slash, pos + 1);
//
//                    String filePath = newFilePath.substring(pos + 1);
//                    //smb旧文件改名
//                    String tempNewFileName = fileName.replace(extName, "-" + System.currentTimeMillis() + extName);
//                    String tempNewFilePath = filePath.replace(newFileName, tempNewFileName);
//                    storage.rename(filePath, tempNewFilePath);
//                }

                AbsFile dfile = storage.write(newFilePath, inputStreamW);
                String reportUrlPdf;
                if (SmbStorage.handle(newFilePath) && storage.fileExists(newFilePath)) {
                    URI uri = dfile.toURI(true);
                    reportUrlPdf = URLDecoder.decode(uri.toString(), Const.charset_UTF_8);
                } else {
                    reportUrlPdf = newFilePath;
                }


                report.setReportUrlPdf(FileUrlDesUtil.encode(reportUrlPdf));
                mapper.saveDoc(report);
            } catch (Exception err) {
                log.error("签名pdf保存失败，错误信息{}", err.getMessage());
                throw new RuntimeException("签名pdf保存失败，错误信息：" + err.getMessage());
            }

        } catch (Exception err) {
            log.error("pdf签名失败，错误信息{}", err.getMessage());
            throw new RuntimeException("pdf签名失败，错误信息：" + err.getMessage());
        } finally {
            storage.release();
        }
        return 0;
    }

    public String saveAuditFile(ExamInfo examInfo, MultipartFile file,SysUser loginUser) throws Exception {
        if (null == examInfo.getId() || null == file) {
            throw new IllegalArgumentException("请选择检查和导入的文件。");
        }
        //获取报告存储
        StorageInterface<?, ?> storage = getReportStorage();

        String fileName = examInfo.getExamSerialNo() + ".pdf";
//        SysUser auditUser = userService.selectUserByUserName(examInfo.getAuditDoctor().getUserName());
        String filePath = FileUtil.getReportAuditFileUrl(loginUser.getDept(),examInfo, fileName);

        try {
            String reportUrlPdf;
            URI uri = FileUtil.storageFile(file, storage, filePath);
            reportUrlPdf = URLDecoder.decode(uri.toString(), Const.charset_UTF_8);
            examInfo.setReportUrlPdf(FileUrlDesUtil.encode(reportUrlPdf));
            mapper.uploadReportUpdateExam(examInfo);
        } finally {
            storage.release();
        }

        return examInfo.getReportUrlPdf();
    }

    @Transactional
    public void auditReport(ExamInfo examInfoI) {
        Long examInfoId = examInfoI.getId();

        //检查信息
        ExamInfo examInfo = new ExamInfo();
        examInfo.setId(examInfoId);
        examInfo = mapper.selectOne(examInfo);
        //更新上传文件名
        examInfo.setReportUploadFilename(examInfoI.getReportUploadFilename());
        SysDictData resultStatus = examInfo.getResultStatus();
        SysDictData lastResultStatus = examInfoI.getResultStatus();
        SysUser auditDoc = SecurityUtils.getLoginUser().getUser();
        //更新不同审核阶段的状态
        if (ResultStatus.AUDIT.is(lastResultStatus.getDictValue())) { //更新二审状态
            examInfo.setSecondAuditDoctor(auditDoc);
            examInfo.setSecondAuditTime(DateUtils.getNowDate());
            lastResultStatus.setDictValue(ResultStatus.SECOND_AUDIT.getValue());
            resultStatus.setDictValue(ResultStatus.SECOND_AUDIT.getValue());
        } else if (ResultStatus.SECOND_AUDIT.is(lastResultStatus.getDictValue())) { //更新三审状态
            examInfo.setThirdAuditDoctor(auditDoc);
            examInfo.setThirdAuditTime(DateUtils.getNowDate());
            lastResultStatus.setDictValue(ResultStatus.THIRD_AUDIT.getValue());
            resultStatus.setDictValue(ResultStatus.THIRD_AUDIT.getValue());
        } else { //更新初审状态
            if (null == examInfo.getExamTime()) {
                examInfo.setExamTime(DateUtils.getNowDate());
                examInfo.setExamDoctor(auditDoc);
            }
            //examInfo.setReportDoctor(auditDoc); 报告医生只有在提交报告时才能修改
            //examInfo.setReportTime(DateUtils.getNowDate());
            examInfo.setReportNo(examInfoI.getReportNo());
            lastResultStatus.setDictValue(ResultStatus.AUDIT.getValue());
            resultStatus.setDictValue(ResultStatus.AUDIT.getValue());
            examInfo.setAuditDoctor(auditDoc);
            examInfo.setAuditTime(DateUtils.getNowDate());
        }
        mapper.uploadReportUpdateExam(examInfo);
    }
}
