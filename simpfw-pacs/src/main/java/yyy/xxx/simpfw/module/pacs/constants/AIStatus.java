package yyy.xxx.simpfw.module.pacs.constants;

public enum AIStatus {

    AI(0),

    MANUAL(1);

    final int code;

    public int getCode() {
        return code;
    }

    AIStatus(int code) {
        this.code = code;
    }

    public static AIStatus fromCode(int code) {
        for (AIStatus status : AIStatus.values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown AIStatus code: " + code);
    }
}
