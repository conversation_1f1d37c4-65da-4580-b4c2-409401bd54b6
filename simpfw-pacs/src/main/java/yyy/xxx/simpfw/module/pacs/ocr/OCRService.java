package yyy.xxx.simpfw.module.pacs.ocr;

import java.awt.image.BufferedImage;
import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.imageio.ImageIO;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import lombok.extern.slf4j.Slf4j;
import yyy.xxx.simpfw.common.annotation.DataSource;
import yyy.xxx.simpfw.common.enums.DataSourceType;
import yyy.xxx.simpfw.common.utils.StringUtils;
import yyy.xxx.simpfw.common.utils.file.ImageUtils;
import yyy.xxx.simpfw.module.pacs.service.ExamInfoService;
import yyy.xxx.simpfw.module.pacs.service.impl.ExamFileService;
import yyy.xxx.simpfw.system.service.ISysConfigService;

/**
 * <AUTHOR>
 * @since 2024/11/2 22:58
 */
@Service("ocrService")
@Slf4j
@DataSource(value = DataSourceType.SLAVE)
public class OCRService {

    private static final String ofnmConfigKey = "ocrFieldNameMapping";

    private static final String ocrServerUrlKey = "ocrServer";
    private static final int maxRetryNum = 1;
    private final ExamFileService examFileService;

    private ISysConfigService sysConfigService;

    private ExamInfoService examInfoService;

    public OCRService(ISysConfigService sysConfigService,
                      ExamInfoService examInfoService,
                      ExamFileService examFileService) {
        this.sysConfigService = sysConfigService;
        this.examInfoService = examInfoService;
        this.examFileService = examFileService;
    }

    @DataSource(value = DataSourceType.MASTER)
    private String getSysConfig(String key) {
        return sysConfigService.selectConfigByKey(key);
    }

    public Map<String, JSONObject> getExamMatchRules() {
        String ocrMatchRulesConfigKey = "ocrMatchRules";
        String ocrMatchRulesConfig = getSysConfig(ocrMatchRulesConfigKey);

        if (null == ocrMatchRulesConfig) {
            throw new IllegalArgumentException(String.format("请添加配置：%s", ocrMatchRulesConfigKey));
        }
        return JSON.parseObject(ocrMatchRulesConfig, Map.class);
    }

    public Map<String, List<FunctionDefinition>> getAllFunctionMapping() {
        Map<String, JSONObject> examMatchRules = getExamMatchRules();
        Map<String, List<FunctionDefinition>> AllFunctionMap = new HashMap<>();
        for (JSONObject matchRuleJson : examMatchRules.values()) {
            OCRMatchRule matchRule = matchRuleJson.toJavaObject(OCRMatchRule.class);
            AllFunctionMap.putAll(matchRule.getFindFunction());
        }
        return AllFunctionMap;
    }

    public String getOCRServerUrl() {
        String ocrServerConfig = getSysConfig(ocrServerUrlKey);
        if (StringUtils.isBlank(ocrServerConfig)) {
            throw new IllegalArgumentException(String.format("请添加配置：%s", ocrServerUrlKey));
        }
        return ocrServerConfig;
    }

    public OCRRecResult findValuesByMatchRule(OCRMatchRule matchRule,
                                              List<OCRTextInfo> ocrResults) {
        return findValuesByMatchRule(null, null, matchRule, ocrResults);
    }

    public OCRRecResult findValuesByMatchRule(String serverURL,
                                              byte[] file,
                                              OCRMatchRule matchRule,
                                              List<OCRTextInfo> ocrResults) {
        return findValuesByMatchRule(serverURL, file, matchRule, ocrResults, 0);
    }

    public OCRRecResult findValuesByMatchRule(String serverURL,
                                              byte[] file,
                                              OCRMatchRule matchRule,
                                              List<OCRTextInfo> ocrResults,
                                              int retryNum) {
        boolean foundFunc = false;
        List<Object> foundValues = new ArrayList<>();
        List<String> ocrTextContents = ocrResults.stream().map(OCRTextInfo::getContent).collect(Collectors.toList());
        List<FunctionDefinition> functionDefinitions = new ArrayList<>();
        String fileLabel = "";
        for (OCRTextInfo ocrResult : ocrResults) {
            for (String functionKey : matchRule.getFindFunction().keySet()) {
                foundFunc = ocrResult.getContent().contains(functionKey);
                if (!foundFunc) {
                    String lowerCaseFunctionKey = functionKey.toLowerCase();
                    String lowerCaseOCRContent = ocrResult.getContent().toLowerCase();
                    foundFunc = lowerCaseOCRContent.contains(lowerCaseFunctionKey);
                }
                if (foundFunc) {
                    functionDefinitions = matchRule
                            .getFindFunction().get(functionKey);
                    log.info("在ocrResult: {} 中找到处理函数 {}: {}", ocrResult, functionKey, functionDefinitions);
                    fileLabel = functionKey;
                    break;
                }
            }
            if (foundFunc) {
                break;
            }
        }
        OCRRecResult ocrRecResult = OCRRecResult.builder()
                .foundValues(foundValues)
                .ocrTextContents(ocrTextContents)
                .fileLabel(fileLabel)
                .build();
        if (!foundFunc) {
            log.warn("没有在检查项目: {} 中找到对应的处理函数", matchRule.getExamItems());
            if (retryNum < maxRetryNum) {
                log.info("对ocr服务发起重试(enableRotationDetection=true)");
                matchRule.getMatchArea().setEnableRotationDetection(true);
                List<OCRTextInfo> ocrTextInfos = pdfOCR(serverURL, file, matchRule.getMatchArea());
                return findValuesByMatchRule(serverURL, file, matchRule, ocrTextInfos, ++retryNum);
            }
            return ocrRecResult;
        }

        Map<String, Integer> matchCountMap = new HashMap<>();
        FieldSetter.clear(); //清空上一个OCR结果缓存的实例

        String fvnmConfig = getSysConfig(ofnmConfigKey);
        if (StringUtils.isBlank(fvnmConfig)) {
            throw new IllegalArgumentException(String.format("请添加配置：%s", ofnmConfigKey));
        }
        Map<String, String> fvnmap = JSON.parseObject(fvnmConfig, Map.class);
        StringBuilder foundValueMsgBuilder = getFoundValueMsgBuilder();
        for (FunctionDefinition functionDefinition : functionDefinitions) {
            if (FunctionType.DIAG_AREA != functionDefinition.getFunctionType()) {
                boolean hasMatch = functionDefinition.getFunctionType().getExecutionHandler()
                        .execute(functionDefinition.getFunctionParam(),
                                ocrResults,
                                matchCountMap,
                                foundValues);
                if (hasMatch) {
                    handleFoundValueMessage(foundValueMsgBuilder,
                            functionDefinition.getFunctionParam().getSaveValues(),
                            functionDefinition.getFunctionParam().getMatchType(),
                            fvnmap);
                    //break;
                }
            }
        }
        if (foundValues.isEmpty()) {
            log.warn("No value found for match rule: {}", matchRule);
        } else {
            ocrRecResult.setFoundValueMsg(foundValueMsgBuilder.toString());
            log.info("Found values {}", foundValues);
        }
        return ocrRecResult;
    }

    private StringBuilder getFoundValueMsgBuilder() {
        //messageBuilder.append("报告识别结果: ");
        return new StringBuilder();
    }

    private void handleFoundValueMessage(StringBuilder messageBuilder,
                                         List<FieldInfo> saveValues,
                                         MatchType matchType,
                                         Map<String, String> fvnmap) {

        StringJoiner messageJoiner = matchType == MatchType.OR ? new StringJoiner(" " + MatchType.OR.name() + " ", "[", "]") : new StringJoiner(" " + MatchType.AND.name() + " ", "[", "]");
        for (FieldInfo saveValue : saveValues) {
            messageJoiner.add(String.format("%s: %s", fvnmap.get(saveValue.getField()), saveValue.getValue()));
        }
        messageBuilder.append(messageJoiner);
    }

    public List<OCRTextInfo> pdfOCR(String serverURL,
                                    byte[] file,
                                    MatchArea matchArea) {
        return pdfOCR(serverURL, file, matchArea, true);
    }

    public List<OCRTextInfo> pdfOCR(String serverURL,
                                    byte[] file,
                                    MatchArea matchArea,
                                    boolean processSpecialChar) {
        String base64s = pdfToImage(file, matchArea);
        OCRRequest ocrRequest = new OCRRequest();
        ocrRequest.appendImage(base64s);
        ocrRequest.setMatchArea(matchArea);
        OCRResponse ocrResponse = requestOCR(ocrRequest, serverURL);
        return processOCRTextInfo(ocrResponse.getOcrResults().get(0).getResult(), processSpecialChar);
    }

    private String pdfToImage(byte[] pdf, MatchArea matchArea) {
        try {
            long startTime = System.currentTimeMillis();
            /*BufferedImage outputImage = getBufferedImage(matchArea, pdf);
            writeImageAsync(outputImage, "png");

            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(outputImage, "jpg", baos);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(pdf);
            String base64s = ImageUtils.imageToBase64(inputStream);*/

            // 关闭流
            /*inputStream.close();
            baos.close();*/

            String base64s = ImageUtils.imageToBase64(pdf);
            long endTime = System.currentTimeMillis();
            log.info("pdf to image total time: {}", (endTime - startTime));
            return base64s;

        } catch (Exception e) {
            throw new RuntimeException("pdf转换图片失败，请确保是图片类（扫描版）pdf或检查pdf文件是否已损坏，文件大小 " + pdf.length / 1024.0 + "KB", e);
        }
    }

    /*private static BufferedImage getBufferedImage(MatchArea matchArea, byte[] pdf) throws IOException {
        PDDocument document = Loader.loadPDF(pdf);
        PDFRenderer pdfRenderer = new PDFRenderer(document);
        long startTime = System.currentTimeMillis();
        BufferedImage bim = pdfRenderer.renderImageWithDPI(matchArea.getPage(), 300);
        long endTime = System.currentTimeMillis();
        log.info("render image with dpi time: {}", (endTime - startTime));

        if (matchArea.getRotate() != 0) {
            int width = bim.getWidth();
            int height = bim.getHeight();

            // 计算旋转后图像的尺寸
            double radian = Math.toRadians(matchArea.getRotate());
            double sin = Math.abs(Math.sin(radian));
            double cos = Math.abs(Math.cos(radian));
            int newWidth = (int) Math.floor(width * cos + height * sin);
            int newHeight = (int) Math.floor(height * cos + width * sin);
            // 创建一个新的图像，用于保存旋转后的结果
            BufferedImage rotatedImage = new BufferedImage(newWidth, newHeight, bim.getType());

            // 获取 Graphics2D 对象
            Graphics2D g2d = rotatedImage.createGraphics();

            // 计算旋转的中心点
            int centerX = newWidth / 2;
            int centerY = newHeight / 2;

            // 在新的图像中旋转原始图像
            g2d.translate(centerX, centerY);  // 移动到旋转中心
            g2d.rotate(radian);               // 旋转
            g2d.translate(-width / 2, -height / 2);  // 将原始图像的左上角移到旋转中心
            // 绘制原始图像
            g2d.drawImage(bim, 0, 0, null);
            // 释放资源
            g2d.dispose();

            bim = rotatedImage;
        }

        // 进行图片截取
        return bim.getSubimage(matchArea.getStartX(),
                matchArea.getStartY(),
                matchArea.getCutWidth() < 0 ? bim.getWidth() - matchArea.getStartX() : matchArea.getCutWidth(),
                matchArea.getCutHeight() < 0 ? bim.getHeight() - matchArea.getStartY() : matchArea.getCutHeight());
    }*/

    private OCRResponse requestOCR(OCRRequest ocrRequest, String serverURL) {
        OCRResponse ocrResponse = null;
        try {
            long startTime = System.currentTimeMillis();
            //创建请求头
            //Create request header
            HttpHeaders headers = new HttpHeaders();
            //设置请求头格式
            //Set the request header format
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<OCRRequest> request = new HttpEntity<>(ocrRequest, headers);
            RestTemplate restTemplate = new RestTemplate();
            //发送请求
            ocrResponse = restTemplate.postForEntity(serverURL, request, OCRResponse.class).getBody();

            if (StringUtils.isNotBlank(ocrResponse.getOcrResults().get(0).getErrMsg())) {
                throw new RuntimeException(ocrResponse.getOcrResults().get(0).getErrMsg());
            }
            long endTime = System.currentTimeMillis();
            log.info("request ocr server total time: {}", (endTime - startTime));
            log.info("OCR Original Text: {}", ocrResponse);
        } catch (Exception e) {
            log.error("ocr解析失败: {}", e.getMessage(), e);
        }
        return ocrResponse;
    }

    public List<OCRTextInfo> processOCRTextInfo(List<OCRTextInfo> ocrTextInfos,
                                                boolean processSpecialChar) {

        for (OCRTextInfo info : ocrTextInfos) {
            if (processSpecialChar) {
                info.setContent(StringUtils.processSpecialChars(info.getContent()));
            }
        }
        // 提取排序后的内容到结果列表
        return ocrTextInfos;
    }

    /**
     * 异步将BufferedImage写入到jar包所在目录
     *
     * @param image  要写入的图片
     * @param format 图片格式(jpg/png等)
     */
    private void writeImageAsync(BufferedImage image, String format) {
        CompletableFuture.supplyAsync(() -> {
            try {
                // 获取jar包所在目录
                ApplicationHome applicationHome = new ApplicationHome(getClass());
                File jarFile = applicationHome.getSource();
                String jarPath = jarFile.getParentFile().toString();

                // 获取当前日期并格式化为两位数字
                String day = LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd"));

                // 构建cut-images目录和日期子目录
                File cutImagesDir = new File(jarPath, "cut-images");
                File dayDir = new File(cutImagesDir, day);
                if (!dayDir.exists()) {
                    dayDir.mkdirs();
                }

                // 检查文件夹大小并清理
                long folderSize = getFolderSize(dayDir);
                long maxSize = 10 * 1024 * 1024; // 10MB
                if (folderSize > maxSize) {
                    log.info("文件夹大小({})超过阈值({}), 开始清理文件",
                            formatFileSize(folderSize), formatFileSize(maxSize));
                    cleanDirectory(dayDir);
                }

                // 生成时间戳文件名
                String timestamp = LocalDateTime.now().format(
                        DateTimeFormatter.ofPattern("HH-mm-ss")
                );

                // 构建完整的文件路径
                String fileName = timestamp + "." + format;
                File outputFile = new File(dayDir, fileName);

                // 写入图片文件
                ImageIO.write(image, format, outputFile);

                return outputFile.getAbsolutePath();

            } catch (Exception e) {
                throw new RuntimeException("Failed to write image", e);
            }
        });
    }

    /**
     * 计算文件夹大小
     *
     * @param directory 目标文件夹
     * @return 文件夹大小（字节）
     */
    private long getFolderSize(File directory) {
        long size = 0;
        if (directory.exists() && directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile()) {
                        size += file.length();
                    }
                }
            }
        }
        return size;
    }

    /**
     * 清空目录中的所有文件
     *
     * @param directory 目标目录
     */
    private void cleanDirectory(File directory) {
        if (directory.exists() && directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile()) {
                        boolean deleted = file.delete();
                        if (!deleted) {
                            log.warn("无法删除文件: {}", file.getAbsolutePath());
                        }
                    }
                }
            }
        }
    }

    /**
     * 格式化文件大小显示
     *
     * @param size 文件大小（字节）
     * @return 格式化后的字符串
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB", size / (1024.0 * 1024 * 1024));
        }
    }

    private static List<OCRTextInfo> parseLog(String logText) {
        List<OCRTextInfo> result = new ArrayList<>();

        // 匹配整个OCRTextInfo对象的模式
        Pattern pattern = Pattern.compile("OCRTextInfo\\(content=([^,]+), coordinate=\\[(.*?)\\]\\)");
        Matcher matcher = pattern.matcher(logText);

        while (matcher.find()) {
            String content = matcher.group(1);
            String coordinatesStr = matcher.group(2);

            // 解析坐标
            List<OCRTextInfo.Coordinate> coordinates = new ArrayList<>();
            Pattern coordPattern = Pattern.compile("OCRTextInfo\\.Coordinate\\(x=([\\d.]+), y=([\\d.]+)\\)");
            Matcher coordMatcher = coordPattern.matcher(coordinatesStr);

            while (coordMatcher.find()) {
                double x = Double.parseDouble(coordMatcher.group(1));
                double y = Double.parseDouble(coordMatcher.group(2));

                coordinates.add(OCRTextInfo.Coordinate.builder()
                        .x(x)
                        .y(y)
                        .build());
            }

            // 构建OCRTextInfo对象
            OCRTextInfo ocrTextInfo = OCRTextInfo.builder()
                    .content(content)
                    .coordinate(coordinates)
                    .build();

            result.add(ocrTextInfo);
        }

        return result;
    }
}
