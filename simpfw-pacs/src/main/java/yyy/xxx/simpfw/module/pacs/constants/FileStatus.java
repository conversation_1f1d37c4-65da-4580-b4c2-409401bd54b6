package yyy.xxx.simpfw.module.pacs.constants;

public enum FileStatus {

    ACTIVE(0),

    DELETED(1);

    final int code;

    public int getCode() {
        return code;
    }

    FileStatus(int code) {
        this.code = code;
    }

    public static FileStatus fromCode(int code) {
        for (FileStatus status : FileStatus.values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown FileStatus code: " + code);
    }
}
