package yyy.xxx.simpfw.module.pacs.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.math.NumberUtils;
import yyy.xxx.simpfw.common.core.domain.entity.SysDept;
import yyy.xxx.simpfw.common.core.domain.entity.SysDictData;
import yyy.xxx.simpfw.common.core.domain.entity.SysUser;
import yyy.xxx.simpfw.common.utils.DateUtils;
import yyy.xxx.simpfw.module.pacs.bo.SignInfo;
import yyy.xxx.simpfw.module.pacs.constants.AIStatus;
import yyy.xxx.simpfw.module.pacs.dto.FileInfoDto;

import java.util.*;

public class ExamInfo extends BaseEntity {

    private String ordExecDoctorCode;
    private String ordExecDoctorName;

    private String examConclusion;

    private AIStatus aiStatus;

    public AIStatus getAiStatus() {
        return aiStatus;
    }

    public void setAiStatus(AIStatus aiStatus) {
        this.aiStatus = aiStatus;
    }

    public String getExamConclusion() {
        return examConclusion;
    }

    public void setExamConclusion(String examConclusion) {
        this.examConclusion = examConclusion;
    }

    public String getOrdExecDoctorCode() {
        return ordExecDoctorCode;
    }

    public void setOrdExecDoctorCode(String ordExecDoctorCode) {
        this.ordExecDoctorCode = ordExecDoctorCode;
    }

    public String getOrdExecDoctorName() {
        return ordExecDoctorName;
    }

    public void setOrdExecDoctorName(String ordExecDoctorName) {
        this.ordExecDoctorName = ordExecDoctorName;
    }

    private List<FileInfoDto> fileInfos;

    public void setFileInfos(List<FileInfoDto> fileInfos) {
        this.fileInfos = fileInfos;
    }

    public List<FileInfoDto> getFileInfos() {
        return fileInfos;
    }

    public Integer getReportDataSource() {
        return reportDataSource;
    }

    public void setReportDataSource(Integer reportDataSource) {
        this.reportDataSource = reportDataSource;
    }

    private Integer reportDataSource;

    private String registWay, examNo, examUid, examBatchNo, examSerialNo, AccessionNumber;

    public String getExamBatchNo() {
        return examBatchNo;
    }

    public void setExamBatchNo(String examBatchNo) {
        this.examBatchNo = examBatchNo;
    }

    public String getExamSerialNo() {
        return examSerialNo;
    }

    public void setExamSerialNo(String examSerialNo) {
        this.examSerialNo = examSerialNo;
    }

    public String getAccessionNumber() {
        return AccessionNumber;
    }

    public void setAccessionNumber(String accessionNumber) {
        AccessionNumber = accessionNumber;
    }

    private String reportNo;

    public List<String> getFileNames() {
        return fileNames;
    }

    public void setFileNames(List<String> fileNames) {
        this.fileNames = fileNames;
    }

    private List<String> fileNames;

    public String getImageNo() {
        return imageNo;
    }

    public void setImageNo(String imageNo) {
        this.imageNo = imageNo;
    }

    private String imageNo;

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    private Patient patientInfo;

    private CallInfo callInfo;

    public Patient getPatientInfo() {
        return patientInfo;
    }
    public void setPatientInfo(Patient patientInfo) {
        this.patientInfo = patientInfo;
    }

    private SysDictData examModality, inpType, examItem, inpRoom, examCostType, inpWard, condParting;

    private EquipRoom equipRoom;
    //检查状态/工作状态，阴阳性
    private SysDictData resultStatus, examResultProp, operationGrade;

    private List<ExamParts> examParts;

    private SysUser registUser;
    private SysUser examDoctor;
    private SysUser reqDoctor;
    private SysUser auditDoctor;
    private SysUser secondAuditDoctor;
    private SysUser thirdAuditDoctor;
    private SysUser reauditDoctor;
    private SysUser signDoctor;
    private SysUser anesDoctor;
    private SysUser operNurse;

    public SysUser getRegistUser() {
        return registUser;
    }

    public void setRegistUser(SysUser registUser) {
        this.registUser = registUser;
    }

    public SysUser getReportDoctor() {
        return reportDoctor;
    }

    public void setReportDoctor(SysUser reportDoctor) {
        this.reportDoctor = reportDoctor;
    }

    private SysUser reportDoctor;
    private SysDept reqDept, examDept;

    private Double examCost;
    private Integer greenChannelFlag, urgencyReport;
    /**
     * 报告发送状态：0未发送，1审核后首次发送返回，2发送/重发失败，3重发成功
     */
    private Integer statusOfSendReport;
    private Date reqTime, appointTime, examTime, auditTime, secondAuditTime, thirdAuditTime, reauditTime, signTime, reportTime;
    private String admNo, clinicDiagnosis, allergyHistory, clinicDisease, inpNo, bedNo, operationInfo, admSeriesNum
            , applyPath, applyId, ordId, ordName, ordPriorityCode, ordPriority, ordBillStatus, arcimCode, examPurpose
            , ordStatus, examDesc, operationSuggestion, originId, examDiagnosis, outpNo;
    private String unexecuteOrdId;

    private List<String> unexecuteOrdIds;

    private List<String> ordIds;

    private SignInfo signInfo;

    public List<String> getUnexecuteOrdIds() {
		return unexecuteOrdIds;
	}

	public void setUnexecuteOrdIds(List<String> unexecuteOrdIds) {
		this.unexecuteOrdIds = unexecuteOrdIds;
	}

    public List<String> getOrdIds() {
        return ordIds;
    }

    public void setOrdIds(List<String> ordIds) {
        this.ordIds = ordIds;
    }

    public SignInfo getSignInfo() {
        return signInfo;
    }

    public void setSignInfo(SignInfo signInfo) {
        this.signInfo = signInfo;
    }

    public String getUnexecuteOrdId() {
        return unexecuteOrdId;
    }

    public void setUnexecuteOrdId(String unexecuteOrdId) {
        this.unexecuteOrdId = unexecuteOrdId;
    }

    public SysDictData getInspectionOrg() {
        return inspectionOrg;
    }

    public void setInspectionOrg(SysDictData inspectionOrg) {
        this.inspectionOrg = inspectionOrg;
    }

    private SysDictData inspectionOrg;

    private Integer examPrerequire, reservedNoUsed, examAtPm, emergency, inpTimes;

    private String signImage, secondSignImage, thirdSignImage;

    private String examDoctorsName, examDoctorsCode, consultantsName, consultantsCode, recordersName, recordersCode;

    private DicomInfo examDevice;

    private SysDictData finalAuditStatus;

    public SysDictData getFinalAuditStatus() {
        return finalAuditStatus;
    }

    public void setFinalAuditStatus(SysDictData finalAuditStatus) {
        this.finalAuditStatus = finalAuditStatus;
    }

    private String reportUrlPdf;

    public String getReportUrlOrgPdf() {
        return reportUrlOrgPdf;
    }

    public void setReportUrlOrgPdf(String reportUrlOrgPdf) {
        this.reportUrlOrgPdf = reportUrlOrgPdf;
    }

    private String reportUrlOrgPdf;
    private String reportUrlJpg;
    private String reportUrlUsername;
    private String reportUrlPassword;

    private String reportUploadFilename;

    private List<String> reportDesignImg;

    private String examAge;

    public String getExamAge() {
        return examAge;
    }

    public void setExamAge(String examAge) {
        this.examAge = examAge;
    }

    public String getReportUploadFilename() {
        return reportUploadFilename;
    }

    public void setReportUploadFilename(String reportUploadFilename) {
        this.reportUploadFilename = reportUploadFilename;
    }

    public List<String> getReportDesignImg() {
        return reportDesignImg;
    }

    public void setReportDesignImg(List<String> reportDesignImg) {
        this.reportDesignImg = reportDesignImg;
    }

    public String getOperationGradeCode() {
        return operationGradeCode;
    }

    public void setOperationGradeCode(String operationGradeCode) {
        this.operationGradeCode = operationGradeCode;
    }

    private String operationGradeCode;

    public String getPathologyDiagnosis() {
        return pathologyDiagnosis;
    }

    public void setPathologyDiagnosis(String pathologyDiagnosis) {
        this.pathologyDiagnosis = pathologyDiagnosis;
    }

    public String getPathologyTreatment() {
        return pathologyTreatment;
    }

    public void setPathologyTreatment(String pathologyTreatment) {
        this.pathologyTreatment = pathologyTreatment;
    }

    public SysDictData getAnesWay() {
        return anesWay;
    }

    public void setAnesWay(SysDictData anesWay) {
        this.anesWay = anesWay;
    }

    public SysDictData getMirrorMeasure() {
        return mirrorMeasure;
    }

    public void setMirrorMeasure(SysDictData mirrorMeasure) {
        this.mirrorMeasure = mirrorMeasure;
    }

    public SysDictData getBreatherWay() {
        return breatherWay;
    }

    public void setBreatherWay(SysDictData breatherWay) {
        this.breatherWay = breatherWay;
    }

    public String getRetainSpecimenFlag() {
        return retainSpecimenFlag;
    }

    public List<SysDictData> getOperationComplicationsCode() {
        return operationComplicationsCode;
    }

    public void setOperationComplicationsCode(List<SysDictData> operationComplicationsCode) {
        this.operationComplicationsCode = operationComplicationsCode;
    }

    public String getRsTreatmentMeasure() {
        return rsTreatmentMeasure;
    }

    public void setRsTreatmentMeasure(String rsTreatmentMeasure) {
        this.rsTreatmentMeasure = rsTreatmentMeasure;
    }

    public String getOcTreatmentMeasure() {
        return ocTreatmentMeasure;
    }

    public void setOcTreatmentMeasure(String ocTreatmentMeasure) {
        this.ocTreatmentMeasure = ocTreatmentMeasure;
    }

    private String pathologyDiagnosis;
    private String pathologyTreatment;

    private SysDictData anesWay;
    private SysDictData mirrorMeasure;
    private SysDictData breatherWay;

    public void setRetainSpecimenFlag(String retainSpecimenFlag) {
        this.retainSpecimenFlag = retainSpecimenFlag;
    }

    public String getOperationComplicationFlag() {
        return operationComplicationFlag;
    }

    public void setOperationComplicationFlag(String operationComplicationFlag) {
        this.operationComplicationFlag = operationComplicationFlag;
    }

    private String retainSpecimenFlag;
    private String operationComplicationFlag;

    private List<SysDictData> operationComplicationsCode;
    private String rsTreatmentMeasure;
    private String ocTreatmentMeasure;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date appointExamDate;

    private String appointExamTime;

    private SysUser operDoctor;

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public SysUser getOperDoctor() {
        return operDoctor;
    }

    public void setOperDoctor(SysUser operDoctor) {
        this.operDoctor = operDoctor;
    }

    public String getAppointExamTime() {
        return appointExamTime;
    }

    public void setAppointExamTime(String appointExamTime) {
        this.appointExamTime = appointExamTime;
    }

    public Date getAppointExamDate() {
        return appointExamDate;
    }

    public void setAppointExamDate(Date appointExamDate) {
        this.appointExamDate = appointExamDate;
    }

    public String getSignImage() {
        return signImage;
    }
    public void setSignImage(String signImage) {
        this.signImage = signImage;
    }

    public String getSecondSignImage() {
        return secondSignImage;
    }

    public void setSecondSignImage(String secondSignImage) {
        this.secondSignImage = secondSignImage;
    }

    public String getThirdSignImage() {
        return thirdSignImage;
    }

    public void setThirdSignImage(String thirdSignImage) {
        this.thirdSignImage = thirdSignImage;
    }

    public String getOriginId() {
        return originId;
    }
    public void setOriginId(String originId) {
        this.originId = originId;
    }

    public SysUser getSignDoctor() {
        return signDoctor;
    }
    public void setSignDoctor(SysUser signDoctor) {
        this.signDoctor = signDoctor;
    }

    public Date getSignTime() {
        return signTime;
    }

    public void setSignTime(Date signTime) {
        this.signTime = signTime;
    }

    private List<ExamAttachment> images;

    //private DicomStudy dicomStudy;
    private List<DicomStudy> dicomStudies;

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public Date getSecondAuditTime() {
        return secondAuditTime;
    }

    public void setSecondAuditTime(Date secondAuditTime) {
        this.secondAuditTime = secondAuditTime;
    }

    public Date getThirdAuditTime() {
        return thirdAuditTime;
    }

    public void setThirdAuditTime(Date thirdAuditTime) {
        this.thirdAuditTime = thirdAuditTime;
    }

    public Date getReauditTime() {
        return reauditTime;
    }

    public void setReauditTime(Date reauditTime) {
        this.reauditTime = reauditTime;
    }

    public SysUser getReauditDoctor() {
        return reauditDoctor;
    }

    public void setReauditDoctor(SysUser reauditDoctor) {
        this.reauditDoctor = reauditDoctor;
    }

    public SysUser getAuditDoctor() {
        return auditDoctor;
    }

    public void setAuditDoctor(SysUser auditDoctor) {
        this.auditDoctor = auditDoctor;
    }

    public SysUser getSecondAuditDoctor() {
        return secondAuditDoctor;
    }

    public void setSecondAuditDoctor(SysUser secondAuditDoctor) {
        this.secondAuditDoctor = secondAuditDoctor;
    }

    public SysUser getThirdAuditDoctor() {
        return thirdAuditDoctor;
    }

    public void setThirdAuditDoctor(SysUser thirdAuditDoctor) {
        this.thirdAuditDoctor = thirdAuditDoctor;
    }

    public String getRegistWay() {
        return registWay;
    }

    public void setRegistWay(String registWay) {
        this.registWay = registWay;
    }

    public CallInfo getCallInfo() {
        return callInfo;
    }

    public SysDictData getCondParting() {
        return condParting;
    }

    public void setCondParting(SysDictData condParting) {
        this.condParting = condParting;
    }

    public void setCallInfo(CallInfo callInfo) {
        this.callInfo = callInfo;
    }

    public DicomStudy getDicomStudy() {
        return null != dicomStudies && !dicomStudies.isEmpty()? dicomStudies.get(0) : null;//dicomStudy;
    }
    @Deprecated
    public void setDicomStudy(DicomStudy dicomStudy) {
        //this.dicomStudy = dicomStudy;
        dicomStudies = new ArrayList<>();
        dicomStudies.add(dicomStudy);
    }

    public SysDictData getExamResultProp() {
        return examResultProp;
    }

    public void setExamResultProp(SysDictData examResultProp) {
        this.examResultProp = examResultProp;
    }

    public String getOperationSuggestion() {
        return operationSuggestion;
    }
    public void setOperationSuggestion(String operationSuggestion) {
        this.operationSuggestion = operationSuggestion;
    }

    public SysDictData getResultStatus() {
        return resultStatus;
    }
    public void setResultStatus(SysDictData resultStatus) {
        this.resultStatus = resultStatus;
    }

    public String getExamNo() {
        return examNo;
    }

    public void setExamNo(String examNo) {
        this.examNo = examNo;
    }

    public Integer getEmergency() {
        return emergency;
    }

    public void setEmergency(Integer emergency) {
        this.emergency = emergency;
    }

    public Integer getUrgencyReport() {
        return urgencyReport;
    }

    public void setUrgencyReport(Integer urgencyReport) {
        this.urgencyReport = urgencyReport;
    }

    public String getExamDesc() {
        return examDesc;
    }

    public void setExamDesc(String examDesc) {
        this.examDesc = examDesc;
    }

    public String getOrdStatus() {
        return ordStatus;
    }

    public void setOrdStatus(String ordStatus) {
        this.ordStatus = ordStatus;
    }

    public Date getExamTime() {
        return examTime;
    }

    public void setExamTime(Date examTime) {
        this.examTime = examTime;
    }

    public String getExamPurpose() {
        return examPurpose;
    }

    public void setExamPurpose(String examPurpose) {
        this.examPurpose = examPurpose;
    }

    public SysDept getExamDept() {
        return examDept;
    }

    public void setExamDept(SysDept examDept) {
        this.examDept = examDept;
    }

    public Date getReqTime() {
        return reqTime;
    }

    public void setReqTime(Date reqTime) {
        this.reqTime = reqTime;
    }

    public SysDictData getExamModality() {
        return examModality;
    }

    public void setExamModality(SysDictData examModality) {
        this.examModality = examModality;
    }

    public SysDictData getInpType() {
        return inpType;
    }

    public void setInpType(SysDictData inpType) {
        this.inpType = inpType;
    }

    public SysDictData getExamItem() {
        return examItem;
    }

    public void setExamItem(SysDictData examItem) {
        this.examItem = examItem;
    }

    public EquipRoom getEquipRoom() {
        return equipRoom;
    }

    public void setEquipRoom(EquipRoom equipRoom) {
        this.equipRoom = equipRoom;
    }

    public List<ExamParts> getExamParts() {
        return examParts;
    }

    public void setExamParts(List<ExamParts> examParts) {
        this.examParts = examParts;
    }

    /**
     * @deprecated 用examDoctorsName, examDoctorsCode？
     */
    public SysUser getExamDoctor() {
        return examDoctor;
    }

    public void setExamDoctor(SysUser examDoctor) {
        this.examDoctor = examDoctor;
    }

    public SysUser getReqDoctor() {
        return reqDoctor;
    }

    public void setReqDoctor(SysUser reqDoctor) {
        this.reqDoctor = reqDoctor;
    }

    public SysDept getReqDept() {
        return reqDept;
    }

    public void setReqDept(SysDept reqDept) {
        this.reqDept = reqDept;
    }

    public Double getExamCost() {
        return examCost;
    }

    public void setExamCost(Double examCost) {
        this.examCost = examCost;
    }

    public Integer getGreenChannelFlag() {
        return greenChannelFlag;
    }

    public void setGreenChannelFlag(Integer greenChannelFlag) {
        this.greenChannelFlag = greenChannelFlag;
    }

    public Date getAppointTime() {
        return appointTime;
    }

    public void setAppointTime(Date appointTime) {
        this.appointTime = appointTime;
    }

    public String getClinicDiagnosis() {
        return clinicDiagnosis;
    }

    public void setClinicDiagnosis(String clinicDiagnosis) {
        this.clinicDiagnosis = clinicDiagnosis;
    }

    public String getAllergyHistory() {
        return allergyHistory;
    }

    public void setAllergyHistory(String allergyHistory) {
        this.allergyHistory = allergyHistory;
    }

    public String getClinicDisease() {
        return clinicDisease;
    }

    public void setClinicDisease(String clinicDisease) {
        this.clinicDisease = clinicDisease;
    }

    public Integer getExamPrerequire() {
        return examPrerequire;
    }

    public void setExamPrerequire(Integer examPrerequire) {
        this.examPrerequire = examPrerequire;
    }

    public Integer getReservedNoUsed() {
        return reservedNoUsed;
    }

    public void setReservedNoUsed(Integer reservedNoUsed) {
        this.reservedNoUsed = reservedNoUsed;
    }

    public Integer getExamAtPm() {
        return examAtPm;
    }

    public void setExamAtPm(Integer examAtPm) {
        this.examAtPm = examAtPm;
    }

    public String getAdmNo() {
        return admNo;
    }

    public void setAdmNo(String admNo) {
        this.admNo = admNo;
    }

    public SysDictData getExamCostType() {
        return examCostType;
    }

    public void setExamCostType(SysDictData examCostType) {
        this.examCostType = examCostType;
    }

    public SysDictData getInpRoom() {
        return inpRoom;
    }

    public void setInpRoom(SysDictData inpRoom) {
        this.inpRoom = inpRoom;
    }

    public String getInpNo() {
        return inpNo;
    }

    public void setInpNo(String inpNo) {
        this.inpNo = inpNo;
    }

    public SysDictData getInpWard() {
        return inpWard;
    }

    public void setInpWard(SysDictData inpWard) {
        this.inpWard = inpWard;
    }

    public String getBedNo() {
        return bedNo;
    }

    public void setBedNo(String bedNo) {
        this.bedNo = bedNo;
    }

    public String getOperationInfo() {
        return operationInfo;
    }

    public void setOperationInfo(String operationInfo) {
        this.operationInfo = operationInfo;
    }

    public String getAdmSeriesNum() {
        return admSeriesNum;
    }

    public void setAdmSeriesNum(String admSeriesNum) {
        this.admSeriesNum = admSeriesNum;
    }

    public String getApplyPath() {
        return applyPath;
    }

    public void setApplyPath(String applyPath) {
        this.applyPath = applyPath;
    }

    public String getOrdId() {
        return ordId;
    }

    public void setOrdId(String ordId) {
        this.ordId = ordId;
    }

    public String getOrdPriorityCode() {
        return ordPriorityCode;
    }

    public void setOrdPriorityCode(String ordPriorityCode) {
        this.ordPriorityCode = ordPriorityCode;
    }

    public String getOrdBillStatus() {
        return ordBillStatus;
    }

    public void setOrdBillStatus(String ordBillStatus) {
        this.ordBillStatus = ordBillStatus;
    }

    public String getArcimCode() {
        return arcimCode;
    }

    public void setArcimCode(String arcimCode) {
        this.arcimCode = arcimCode;
    }

    public String getOrdName() {
        return ordName;
    }

    public void setOrdName(String ordName) {
        this.ordName = ordName;
    }

    public String getOrdPriority() {
        return ordPriority;
    }

    public void setOrdPriority(String ordPriority) {
        this.ordPriority = ordPriority;
    }
	public List<ExamAttachment> getImages() {
		return images;
	}
	public void setImages(List<ExamAttachment> images) {
		this.images = images;
	}

    public Date getReportTime() {
        return reportTime;
    }
    public void setReportTime(Date reportTime) {
        this.reportTime = reportTime;
    }

    @Override
    public boolean equals(Object other) {
        if(null == other) {
            return false;
        }
        //if(this.getClass() != other.getClass()) {
        if(!(other instanceof ExamInfo)) {
            return  false;
        }
        ExamInfo other0 = (ExamInfo) other;
        return null != getId() && null != other0.getId() && getId().longValue() == other0.getId().longValue();
    }

    @Override
    public int hashCode() {
        if(null == getId()) {
            return super.hashCode();
        }

        return getId().hashCode();
    }
	public Integer getInpTimes() {
		return inpTimes;
	}
	public void setInpTimes(Integer inpTimes) {
		this.inpTimes = inpTimes;
	}
	public String getExamDiagnosis() {
		return examDiagnosis;
	}
	public void setExamDiagnosis(String examDiagnosis) {
		this.examDiagnosis = examDiagnosis;
	}

    public String getExamDoctorsName() {
        return examDoctorsName;
    }
    public void setExamDoctorsName(String examDoctorsName) {
        this.examDoctorsName = examDoctorsName;
    }

    public String getExamDoctorsCode() {
        return examDoctorsCode;
    }
    public void setExamDoctorsCode(String examDoctorsCode) {
        this.examDoctorsCode = examDoctorsCode;
    }

    public String getConsultantsName() {
        return consultantsName;
    }
    public void setConsultantsName(String consultantsName) {
        this.consultantsName = consultantsName;
    }

    public String getConsultantsCode() {
        return consultantsCode;
    }
    public void setConsultantsCode(String consultantsCode) {
        this.consultantsCode = consultantsCode;
    }

    public String getRecordersName() {
        return recordersName;
    }
    public void setRecordersName(String recordersName) {
        this.recordersName = recordersName;
    }

    public String getRecordersCode() {
        return recordersCode;
    }
    public void setRecordersCode(String recordersCode) {
        this.recordersCode = recordersCode;
    }

    public String getOutpNo() {
        return outpNo;
    }
    public void setOutpNo(String outpNo) {
        this.outpNo = outpNo;
    }

    public DicomInfo getExamDevice() {
        return examDevice;
    }
    public void setExamDevice(DicomInfo examDevice) {
        this.examDevice = examDevice;
    }

    public String getReportUrlPdf() {
        return reportUrlPdf;
    }
    public void setReportUrlPdf(String reportUrlPdf) {
        this.reportUrlPdf = reportUrlPdf;
    }

    public String getReportUrlJpg() {
        return reportUrlJpg;
    }
    public void setReportUrlJpg(String reportUrlJpg) {
        this.reportUrlJpg = reportUrlJpg;
    }

    @JsonIgnore
    public String getReportUrlUsername() {
        return reportUrlUsername;
    }
    public void setReportUrlUsername(String reportUrlUsername) {
        this.reportUrlUsername = reportUrlUsername;
    }

    @JsonIgnore
    public String getReportUrlPassword() {
        return reportUrlPassword;
    }
    public void setReportUrlPassword(String reportUrlPassword) {
        this.reportUrlPassword = reportUrlPassword;
    }

    public List<DicomStudy> getDicomStudies() {
        return dicomStudies;
    }
    public void setDicomStudies(List<DicomStudy> dicomStudies) {
        this.dicomStudies = dicomStudies;
    }

    public Integer getStatusOfSendReport() {
        return statusOfSendReport;
    }
    public void setStatusOfSendReport(Integer statusOfSendReport) {
        this.statusOfSendReport = statusOfSendReport;
    }

    public String getExamUid() {
        return examUid;
    }
    public void setExamUid(String examUid) {
        this.examUid = examUid;
    }
    
	public SysDictData getOperationGrade() {
		return operationGrade;
	}
	public void setOperationGrade(SysDictData operationGrade) {
		this.operationGrade = operationGrade;
	}
	
	public SysUser getAnesDoctor() {
		return anesDoctor;
	}
	public void setAnesDoctor(SysUser anesDoctor) {
		this.anesDoctor = anesDoctor;
	}
	
	public SysUser getOperNurse() {
		return operNurse;
	}
	public void setOperNurse(SysUser operNurse) {
		this.operNurse = operNurse;
	}


    /**
     * 检查项目列表
     */
    private List<String> examItemCodes;
    public List<String> getExamItemCodes() {
        return examItemCodes;
    }
    public void setExamItemCodes(List<String> examItemCodes) {
        this.examItemCodes = examItemCodes;
    }
    /**
     * 排除检查准备: 无需诊前准备或已准备就绪
     */
    private Boolean examPrerequireExclude;
    public Boolean getExamPrerequireExclude() {
        return examPrerequireExclude;
    }
    public void setExamPrerequireExclude(Boolean examPrerequireExclude) {
        this.examPrerequireExclude = examPrerequireExclude;
    }

    /**
     * 工作状态列表
     */
    private List<String> resultStatusValues;
    public List<String> getResultStatusValues() {
        return resultStatusValues;
    }
    public void setResultStatusValues(List<String> resultStatusValues) {
        this.resultStatusValues = resultStatusValues;
    }

    private String resultStatusAsStatus;
    public String getResultStatusAsStatus() {
        return resultStatusAsStatus;
    }
    public void setResultStatusAsStatus(String resultStatusAsStatus) {
        this.resultStatusAsStatus = resultStatusAsStatus;
    }

    /**
     * 检查医生列表
     */
    private List<String> examDoctorUserNames;
    public List<String> getExamDoctorUserNames() {
        return examDoctorUserNames;
    }
    public void setExamDoctorUserNames(List<String> examDoctorUserNames) {
        this.examDoctorUserNames = examDoctorUserNames;
    }

    private List<String> reportDoctorUserNames;
    public List<String> getReportDoctorUserNames() {
        return reportDoctorUserNames;
    }
    public void setReportDoctorUserNames(List<String> reportDoctorUserNames) {
        this.reportDoctorUserNames = reportDoctorUserNames;
    }

    /**
     * 登记开始天数
     */
    public void setDatesCreated(String numVal) {
        if(!NumberUtils.isDigits(numVal)) { return; }
        int num = Integer.parseInt(numVal);
        if(num <= 0) {
            return;
        }
        Date date = DateUtils.getNowDate();
        //取时间
        date = DateUtils.truncate(date, Calendar.DAY_OF_MONTH);
        //
        setCreateTimeLt(date);
        //1表示当天，2表示前一天
        if(0 != (-- num)) {
            date = DateUtils.addDays(date, -1 * num);
        }
        setCreateTimeGe(date);
        //
        appointWithCreated = true;
    }

    /**
     * 检查日期
     * @param numVal
     */
    public void setDatesExamed(String numVal) {
        if(!NumberUtils.isDigits(numVal)) { return; }
        int num = Integer.parseInt(numVal);
        if(num <= 0) {
            return;
        }
        Date date = DateUtils.getNowDate();
        //取时间
        date = DateUtils.truncate(date, Calendar.DAY_OF_MONTH);
        //
        setExamTimeLt(date);
        //1表示当天，2表示前一天
        if(0 != (-- num)) {
            date = DateUtils.addDays(date, -1 * num);
        }
        setExamTimeGe(date);
        //
        appointWithCreated = true;
    }

    //登记时间和预约时间同步查询
    private boolean appointWithCreated = false;
    public void setAppointWithCreated(boolean appointWithCreated) {
        this.appointWithCreated = appointWithCreated;
    }

     /**
     * 登记起始日期
     */
    private Date createTimeGe, createTimeLt;

    @Override
    public Date getCreateTimeGe() {
        return createTimeGe;
    }

    @Override
    public void setCreateTimeGe(Date createTimeGe) {
        this.createTimeGe = createTimeGe;
    }

    @Override
    public Date getCreateTimeLt() {
        return createTimeLt;
    }

    @Override
    public void setCreateTimeLt(Date createTimeLt) {
        this.createTimeLt = createTimeLt;
    }

    /**
     * 检查起始日期
     */
    private Date examTimeGe, examTimeLt;
    public Date getExamTimeGe() {
        return examTimeGe;
    }
    public void setExamTimeGe(Date examTimeGe) {
        this.examTimeGe = examTimeGe;
    }
    public Date getExamTimeLt() {
        return examTimeLt;
    }
    public void setExamTimeLt(Date examTimeLt) {
        this.examTimeLt = examTimeLt;
    }
    /**
     * 审核日期起始
     */
    private Date auditTimeGe, auditTimeLt;
    public Date getAuditTimeGe() {
        return auditTimeGe;
    }
    public void setAuditTimeGe(Date auditTimeGe) {
        this.auditTimeGe = auditTimeGe;
    }
    public Date getAuditTimeLt() {
        return auditTimeLt;
    }
    public void setAuditTimeLt(Date auditTimeLt) {
        this.auditTimeLt = auditTimeLt;
    }
    /**
     * 检查所见、检查诊断查询关键字间隔
     */
    private static final String SEGM_CHAR = "[\\s,，]+";
    /**
     * 检查所见查询关键字
     */
    public String[] getExamDescSegm() {
        return null != getExamDesc()? getExamDesc().split(SEGM_CHAR) : null;
    }

    /**
     * 检查诊断查询关键字
     */
    public String[] getExamDiagnosisSegm() {
        return null != getExamDiagnosis()? getExamDiagnosis().split(SEGM_CHAR) : null;
    }
    /**
     * 就诊类型，设备型号
     */
    private String[] inpTypeValues, deviceCodeValues;
    public String[] getInpTypeValues() {
        return inpTypeValues;
    }
    public void setInpTypeValues(String[] inpTypeValues) {
        this.inpTypeValues = inpTypeValues;
    }
    public String[] getDeviceCodeValues() {
        return deviceCodeValues;
    }
    public void setDeviceCodeValues(String[] deviceCodeValues) {
        this.deviceCodeValues = deviceCodeValues;
    }

    private Date appointTimeGe, appointTimeLt;

    public Date getAppointTimeGe() {
        return appointTimeGe;
    }
    public void setAppointTimeGe(Date appointTimeGe) {
        this.appointTimeGe = appointTimeGe;
    }

    private Date appointExamDateGe, appointExamDateLt;;

    public Date getAppointExamDateGe() {
        return appointExamDateGe;
    }

    public void setAppointExamDateGe(Date appointExamDateGe) {
        this.appointExamDateGe = appointExamDateGe;
    }

    public Date getAppointExamDateLt() {
        return appointExamDateLt;
    }

    public void setAppointExamDateLt(Date appointExamDateLt) {
        this.appointExamDateLt = appointExamDateLt;
    }

    public Date getAppointTimeLt() {
        return appointTimeLt;
    }
    public void setAppointTimeLt(Date appointTimeLt) {
        this.appointTimeLt = appointTimeLt;
    }

    /**
     * 设备型号列表
     */
    private List<String> examDevicesCode;
    public List<String> getExamDevicesCode() {
        return examDevicesCode;
    }
    public void setExamDevicesCode(List<String> examDevicesCode) {
        this.examDevicesCode = examDevicesCode;
    }

    /**
     * 检查房间列表
     */
    private List<String> equipRoomsCode;
    public List<String> getEquipRoomsCode() {
        return equipRoomsCode;
    }
    public void setEquipRoomsCode(List<String> equipRoomsCode) {
        this.equipRoomsCode = equipRoomsCode;
    }

    /**
     * 检查类型
     */
    private List<String> examModalitiesCodes;
    public List<String> getExamModalitiesCodes() {
        return examModalitiesCodes;
    }
    public void setExamModalitiesCodes(List<String> examModalitiesCode) {
        this.examModalitiesCodes = examModalitiesCode;
    }

    /**
     * 检查科室列表
     */
    private List<String> examDeptsCode;
    public List<String> getExamDeptsCode() {
        return examDeptsCode;
    }

    public void setExamDeptsCode(List<String> examDeptsCode) {
        this.examDeptsCode = examDeptsCode;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ExamInfo{");
        
        if (reportDataSource != null) sb.append("reportDataSource=").append(reportDataSource).append(", ");
        if (registWay != null) sb.append("registWay='").append(registWay).append("', ");
        if (examNo != null) sb.append("examNo='").append(examNo).append("', ");
        if (examUid != null) sb.append("examUid='").append(examUid).append("', ");
        if (examBatchNo != null) sb.append("examBatchNo='").append(examBatchNo).append("', ");
        if (examSerialNo != null) sb.append("examSerialNo='").append(examSerialNo).append("', ");
        if (AccessionNumber != null) sb.append("AccessionNumber='").append(AccessionNumber).append("', ");
        if (reportNo != null) sb.append("reportNo='").append(reportNo).append("', ");
        if (fileNames != null) sb.append("fileNames=").append(fileNames).append(", ");
        if (imageNo != null) sb.append("imageNo='").append(imageNo).append("', ");
        if (patientInfo != null) sb.append("patientInfo=").append(patientInfo).append(", ");
        if (callInfo != null) sb.append("callInfo=").append(callInfo).append(", ");
        if (examModality != null) sb.append("examModality=").append(examModality).append(", ");
        if (inpType != null) sb.append("inpType=").append(inpType).append(", ");
        if (examItem != null) sb.append("examItem=").append(examItem).append(", ");
        if (inpRoom != null) sb.append("inpRoom=").append(inpRoom).append(", ");
        if (examCostType != null) sb.append("examCostType=").append(examCostType).append(", ");
        if (inpWard != null) sb.append("inpWard=").append(inpWard).append(", ");
        if (condParting != null) sb.append("condParting=").append(condParting).append(", ");
        if (equipRoom != null) sb.append("equipRoom=").append(equipRoom).append(", ");
        if (resultStatus != null) sb.append("resultStatus=").append(resultStatus).append(", ");
        if (examResultProp != null) sb.append("examResultProp=").append(examResultProp).append(", ");
        if (operationGrade != null) sb.append("operationGrade=").append(operationGrade).append(", ");
        if (examParts != null) sb.append("examParts=").append(examParts).append(", ");
        if (examDoctor != null) sb.append("examDoctor=").append(examDoctor).append(", ");
        if (reqDoctor != null) sb.append("reqDoctor=").append(reqDoctor).append(", ");
        if (auditDoctor != null) sb.append("auditDoctor=").append(auditDoctor).append(", ");
        if (reauditDoctor != null) sb.append("reauditDoctor=").append(reauditDoctor).append(", ");
        if (signDoctor != null) sb.append("signDoctor=").append(signDoctor).append(", ");
        if (anesDoctor != null) sb.append("anesDoctor=").append(anesDoctor).append(", ");
        if (operNurse != null) sb.append("operNurse=").append(operNurse).append(", ");
        if (reportDoctor != null) sb.append("reportDoctor=").append(reportDoctor).append(", ");
        if (reqDept != null) sb.append("reqDept=").append(reqDept).append(", ");
        if (examDept != null) sb.append("examDept=").append(examDept).append(", ");
        if (examCost != null) sb.append("examCost=").append(examCost).append(", ");
        if (greenChannelFlag != null) sb.append("greenChannelFlag=").append(greenChannelFlag).append(", ");
        if (urgencyReport != null) sb.append("urgencyReport=").append(urgencyReport).append(", ");
        if (statusOfSendReport != null) sb.append("statusOfSendReport=").append(statusOfSendReport).append(", ");
        if (reqTime != null) sb.append("reqTime=").append(reqTime).append(", ");
        if (appointTime != null) sb.append("appointTime=").append(appointTime).append(", ");
        if (examTime != null) sb.append("examTime=").append(examTime).append(", ");
        if (auditTime != null) sb.append("auditTime=").append(auditTime).append(", ");
        if (reauditTime != null) sb.append("reauditTime=").append(reauditTime).append(", ");
        if (signTime != null) sb.append("signTime=").append(signTime).append(", ");
        if (reportTime != null) sb.append("reportTime=").append(reportTime).append(", ");
        if (admNo != null) sb.append("admNo='").append(admNo).append("', ");
        if (clinicDiagnosis != null) sb.append("clinicDiagnosis='").append(clinicDiagnosis).append("', ");
        if (allergyHistory != null) sb.append("allergyHistory='").append(allergyHistory).append("', ");
        if (clinicDisease != null) sb.append("clinicDisease='").append(clinicDisease).append("', ");
        if (inpNo != null) sb.append("inpNo='").append(inpNo).append("', ");
        if (bedNo != null) sb.append("bedNo='").append(bedNo).append("', ");
        if (operationInfo != null) sb.append("operationInfo='").append(operationInfo).append("', ");
        if (admSeriesNum != null) sb.append("admSeriesNum='").append(admSeriesNum).append("', ");
        if (applyPath != null) sb.append("applyPath='").append(applyPath).append("', ");
        if (ordId != null) sb.append("ordId='").append(ordId).append("', ");
        if (ordName != null) sb.append("ordName='").append(ordName).append("', ");
        if (ordPriorityCode != null) sb.append("ordPriorityCode='").append(ordPriorityCode).append("', ");
        if (ordPriority != null) sb.append("ordPriority='").append(ordPriority).append("', ");
        if (ordBillStatus != null) sb.append("ordBillStatus='").append(ordBillStatus).append("', ");
        if (arcimCode != null) sb.append("arcimCode='").append(arcimCode).append("', ");
        if (examPurpose != null) sb.append("examPurpose='").append(examPurpose).append("', ");
        if (ordStatus != null) sb.append("ordStatus='").append(ordStatus).append("', ");
        if (examDesc != null) sb.append("examDesc='").append(examDesc).append("', ");
        if (operationSuggestion != null) sb.append("operationSuggestion='").append(operationSuggestion).append("', ");
        if (originId != null) sb.append("originId='").append(originId).append("', ");
        if (examDiagnosis != null) sb.append("examDiagnosis='").append(examDiagnosis).append("', ");
        if (outpNo != null) sb.append("outpNo='").append(outpNo).append("', ");
        if (unexecuteOrdId != null) sb.append("unexecuteOrdId='").append(unexecuteOrdId).append("', ");
        if (signInfo != null) sb.append("signInfo=").append(signInfo).append(", ");
        if (examPrerequire != null) sb.append("examPrerequire=").append(examPrerequire).append(", ");
        if (reservedNoUsed != null) sb.append("reservedNoUsed=").append(reservedNoUsed).append(", ");
        if (examAtPm != null) sb.append("examAtPm=").append(examAtPm).append(", ");
        if (emergency != null) sb.append("emergency=").append(emergency).append(", ");
        if (inpTimes != null) sb.append("inpTimes=").append(inpTimes).append(", ");
        if (signImage != null) sb.append("signImage='").append(signImage).append("', ");
        if (examDoctorsName != null) sb.append("examDoctorsName='").append(examDoctorsName).append("', ");
        if (examDoctorsCode != null) sb.append("examDoctorsCode='").append(examDoctorsCode).append("', ");
        if (consultantsName != null) sb.append("consultantsName='").append(consultantsName).append("', ");
        if (consultantsCode != null) sb.append("consultantsCode='").append(consultantsCode).append("', ");
        if (recordersName != null) sb.append("recordersName='").append(recordersName).append("', ");
        if (recordersCode != null) sb.append("recordersCode='").append(recordersCode).append("', ");
        if (examDevice != null) sb.append("examDevice=").append(examDevice).append(", ");
        if (reportUrlPdf != null) sb.append("reportUrlPdf='").append(reportUrlPdf).append("', ");
        if (reportUrlOrgPdf != null) sb.append("reportUrlOrgPdf='").append(reportUrlOrgPdf).append("', ");
        if (reportUrlJpg != null) sb.append("reportUrlJpg='").append(reportUrlJpg).append("', ");
        if (reportUrlUsername != null) sb.append("reportUrlUsername='").append(reportUrlUsername).append("', ");
        if (reportUrlPassword != null) sb.append("reportUrlPassword='").append(reportUrlPassword).append("', ");
        if (reportUploadFilename != null) sb.append("reportUploadFilename='").append(reportUploadFilename).append("', ");
        if (reportDesignImg != null) sb.append("reportDesignImg=").append(reportDesignImg).append(", ");
        if (operationGradeCode != null) sb.append("operationGradeCode='").append(operationGradeCode).append("', ");
        if (pathologyDiagnosis != null) sb.append("pathologyDiagnosis='").append(pathologyDiagnosis).append("', ");
        if (pathologyTreatment != null) sb.append("pathologyTreatment='").append(pathologyTreatment).append("', ");
        if (anesWay != null) sb.append("anesWay=").append(anesWay).append(", ");
        if (mirrorMeasure != null) sb.append("mirrorMeasure=").append(mirrorMeasure).append(", ");
        if (breatherWay != null) sb.append("breatherWay=").append(breatherWay).append(", ");
        if (retainSpecimenFlag != null) sb.append("retainSpecimenFlag='").append(retainSpecimenFlag).append("', ");
        if (operationComplicationFlag != null) sb.append("operationComplicationFlag='").append(operationComplicationFlag).append("', ");
        if (operationComplicationsCode != null) sb.append("operationComplicationsCode=").append(operationComplicationsCode).append(", ");
        if (rsTreatmentMeasure != null) sb.append("rsTreatmentMeasure='").append(rsTreatmentMeasure).append("', ");
        if (ocTreatmentMeasure != null) sb.append("ocTreatmentMeasure='").append(ocTreatmentMeasure).append("', ");
        if (appointExamDate != null) sb.append("appointExamDate=").append(appointExamDate).append(", ");
        if (appointExamTime != null) sb.append("appointExamTime='").append(appointExamTime).append("', ");
        if (operDoctor != null) sb.append("operDoctor=").append(operDoctor).append(", ");
        if (images != null) sb.append("images=").append(images).append(", ");
        if (dicomStudies != null) sb.append("dicomStudies=").append(dicomStudies).append(", ");
        if (examItemCodes != null) sb.append("examItemCodes=").append(examItemCodes).append(", ");
        if (examPrerequireExclude != null) sb.append("examPrerequireExclude=").append(examPrerequireExclude).append(", ");
        if (resultStatusValues != null) sb.append("resultStatusValues=").append(resultStatusValues).append(", ");
        if (resultStatusAsStatus != null) sb.append("resultStatusAsStatus='").append(resultStatusAsStatus).append("', ");
        if (examDoctorUserNames != null) sb.append("examDoctorUserNames=").append(examDoctorUserNames).append(", ");
        if (reportDoctorUserNames != null) sb.append("reportDoctorUserNames=").append(reportDoctorUserNames).append(", ");
        sb.append("appointWithCreated=").append(appointWithCreated).append(", ");
        if (examTimeGe != null) sb.append("examTimeGe=").append(examTimeGe).append(", ");
        if (examTimeLt != null) sb.append("examTimeLt=").append(examTimeLt).append(", ");
        if (auditTimeGe != null) sb.append("auditTimeGe=").append(auditTimeGe).append(", ");
        if (auditTimeLt != null) sb.append("auditTimeLt=").append(auditTimeLt).append(", ");
        if (inpTypeValues != null) sb.append("inpTypeValues=").append(Arrays.toString(inpTypeValues)).append(", ");
        if (deviceCodeValues != null) sb.append("deviceCodeValues=").append(Arrays.toString(deviceCodeValues)).append(", ");
        if (appointTimeGe != null) sb.append("appointTimeGe=").append(appointTimeGe).append(", ");
        if (appointTimeLt != null) sb.append("appointTimeLt=").append(appointTimeLt).append(", ");
        if (appointExamDateGe != null) sb.append("appointExamDateGe=").append(appointExamDateGe).append(", ");
        if (appointExamDateLt != null) sb.append("appointExamDateLt=").append(appointExamDateLt).append(", ");
        if (examDevicesCode != null) sb.append("examDevicesCode=").append(examDevicesCode).append(", ");
        if (equipRoomsCode != null) sb.append("equipRoomsCode=").append(equipRoomsCode).append(", ");
        if (examModalitiesCodes != null) sb.append("examModalitiesCodes=").append(examModalitiesCodes).append(", ");
        if (examDeptsCode != null) sb.append("examDeptsCode=").append(examDeptsCode).append(", ");
        if (examAge != null) sb.append("examAge=").append(examAge).append(", ");
        
        // 移除最后一个逗号和空格（如果存在的话）
        if (sb.toString().endsWith(", ")) {
            sb.setLength(sb.length() - 2);
        }
        
        sb.append('}');
        return sb.toString();
    }
}
