<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="yyy.xxx.simpfw.module.pacs.mapper.ExamInfoMapper">
    
    <resultMap id="resultMapBase" type="yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo" autoMapping="true">
        <!-- <association property="patientInfo" javaType="yyy.xxx.simpfw.module.pacs.vo.PatientVo" autoMapping="true" columnPrefix="p__" /> -->
        <association property="callInfo" autoMapping="true" columnPrefix="ci__" resultMap="callInfoMap" />
        <!-- <association property="dicomStudy" javaType="yyy.xxx.simpfw.module.pacs.entity.DicomStudy" autoMapping="true" columnPrefix="dcs__" /> -->
        <association property="examModality" javaType="SysDictData" autoMapping="true" columnPrefix="rt__" />
        <association property="inpType" javaType="SysDictData" autoMapping="true" columnPrefix="rts__" />
        <association property="examItem" javaType="SysDictData" autoMapping="true" columnPrefix="ei__" />
        <association property="equipRoom" javaType="yyy.xxx.simpfw.module.pacs.entity.EquipRoom" autoMapping="true" columnPrefix="er__" />
        <association property="creator" javaType="SysUser" autoMapping="true" columnPrefix="regu__" />
        <association property="registUser" javaType="SysUser" autoMapping="true" columnPrefix="reg__" />
        <association property="examDoctor" javaType="SysUser" autoMapping="true" columnPrefix="eu__" />
        <association property="reqDoctor" javaType="SysUser" autoMapping="true" columnPrefix="ru__" />
        <association property="auditDoctor" javaType="SysUser" autoMapping="true" columnPrefix="aud__" />
        <association property="secondAuditDoctor" javaType="SysUser" autoMapping="true" columnPrefix="secondaud__" />
        <association property="thirdAuditDoctor" javaType="SysUser" autoMapping="true" columnPrefix="thirdaud__" />
        <association property="reauditDoctor" javaType="SysUser" autoMapping="true" columnPrefix="aud2__" />
        <association property="signDoctor" javaType="SysUser" autoMapping="true" columnPrefix="sig__" />
        <association property="reportDoctor" javaType="SysUser" autoMapping="true" columnPrefix="rpu__" />
        <association property="operDoctor" javaType="SysUser" autoMapping="true" columnPrefix="od__" />
        <association property="reqDept" javaType="SysDept" autoMapping="true" columnPrefix="rd__" />
        <association property="examDept" javaType="SysDept" autoMapping="true" columnPrefix="ex__" />
        <association property="inpWard" javaType="SysDictData" autoMapping="true" columnPrefix="wd__" />
        <association property="inpRoom" javaType="SysDictData" autoMapping="true" columnPrefix="rm__" />
        <association property="examCostType" javaType="SysDictData" autoMapping="true" columnPrefix="cos__" />
        <association property="resultStatus" javaType="SysDictData" autoMapping="true" columnPrefix="res2__" />
        <association property="examResultProp" javaType="SysDictData" autoMapping="true" columnPrefix="erp__" />
        <association property="condParting" javaType="SysDictData" autoMapping="true" columnPrefix="cp__" />
        <association property="inspectionOrg" javaType="SysDictData" autoMapping="true" columnPrefix="org__">
            <association property="extend" javaType="yyy.xxx.simpfw.common.core.domain.model.Extend" autoMapping="true" columnPrefix="ext__">
            </association>
        </association>
        <association property="examDevice" javaType="yyy.xxx.simpfw.module.pacs.entity.DicomInfo" autoMapping="true" columnPrefix="exdev__" />
        <collection property="examParts" ofType="yyy.xxx.simpfw.module.pacs.vo.ExamParts"
                    column="exam_parts_id"
                    select="yyy.xxx.simpfw.module.pacs.mapper.ExamPartsMapper.selectExamParts" />
        <collection property="dicomStudies" ofType="yyy.xxx.simpfw.module.pacs.entity.DicomStudy"
                    column="{examUid=examUid}"
                    select="yyy.xxx.simpfw.module.pacs.mapper.DicomStudyMapper.selectListSelf" />
        <collection property="fileInfos" ofType="yyy.xxx.simpfw.module.pacs.entity.ExamFileEntity"
                    column="{examUid=examUid}"
                    select="yyy.xxx.simpfw.module.pacs.mapper.ExamFileEntityMapper.selectByExamUid"/>
        <collection property="ordIds" ofType="java.lang.String"
                    column="{examInfoId=id}"
                    select="yyy.xxx.simpfw.module.pacs.mapper.MedicalOrdersEntityMapper.selectOrdIdsByExamInfoId"/>
        <collection property="unexecuteOrdIds" ofType="java.lang.String"
                    column="{examInfoId=id, ordStatus=unexecute_ord_status}"
                    select="yyy.xxx.simpfw.module.pacs.mapper.MedicalOrdersEntityMapper.selectOrdIdsByExamInfoId"/>

    </resultMap>
    <!-- 检查完整信息 -->
    <resultMap id="resultMapOutline" type="yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo" extends="resultMapBase" autoMapping="true">
        <association property="patientInfo" javaType="yyy.xxx.simpfw.module.pacs.vo.PatientVo"
         resultMap="yyy.xxx.simpfw.module.pacs.mapper.PatientMapper.resultMap" columnPrefix="p__" />
    </resultMap>
    <!-- 检查列表信息 -->
    <resultMap id="resultMapFull" type="yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo" extends="resultMapBase" autoMapping="true">
        <association property="patientInfo" javaType="yyy.xxx.simpfw.module.pacs.vo.PatientVo"
                     column="{patientId=patient_id}"
                     select="yyy.xxx.simpfw.module.pacs.mapper.PatientMapper.selectOne"/>
    </resultMap>
    <!-- 呼叫信息 -->
    <resultMap id="callInfoMap" type="yyy.xxx.simpfw.module.pacs.entity.CallInfo" autoMapping="true">
        <association property="callRoom" javaType="yyy.xxx.simpfw.module.pacs.entity.EquipRoom">
            <result property="roomCode" column="rm__roomCode"/>
            <result property="roomName" column="rm__roomName"/>
        </association>
    </resultMap>

    <sql id="selectBase">
        select h.id,h.status,h.report_data_source reportDataSource,h.create_time createTime,h.update_time
        updateTime,h.note_info noteInfo
        ,h.exam_uid examUid,h.exam_no examNo,h.patient_id,h.exam_modality_code, h.inp_type_code, h.exam_item_code,
        h.equip_room_code, h.exam_parts_id
        ,h.req_dept_code, h.req_doctor_code, h.exam_cost examCost, h.green_channel_flag greenChannelFlag
        ,h.appoint_time appointTime, h.clinic_diagnosis clinicDiagnosis, h.allergy_history allergyHistory,
        h.clinic_disease clinicDisease, h.exam_prerequire examPrerequire
        ,h.reserved_no_used reservedNoUsed, h.exam_at_pm examAtPm, h.adm_no admNo, h.operation_info operationInfo
        ,h.adm_series_num admSeriesNum,h.inp_no inpNo,h.bed_no bedNo,h.apply_path applyPath,h.exam_diagnosis
        examDiagnosis
        ,h.ord_id ordId, 'V' as unexecute_ord_status, h.unexecute_ord_id unexecuteOrdId,h.ord_name ordName,h.arcim_code
        arcimCode,h.ord_bill_status
        ordBillStatus
        ,h.ord_priority_code ordPriorityCode,h.ord_priority ordPriority,h.exam_purpose examPurpose,h.is_emergency
        emergency
        ,h.ord_status ordStatus,h.req_time reqTime,h.exam_time examTime,operation_suggestion
        operationSuggestion,h.exam_conclusion examDesc ,h.exam_conclusion examConclusion
        ,h.audit_time auditTime,h.second_audit_time secondAuditTime,h.third_audit_time thirdAuditTime,h.reaudit_time
        reauditTime,h.sign_time signTime,h.origin_id originId,h.inp_times inpTimes
        ,h.report_upload_filename reportUploadFilename,h.exam_age examAge
        ,exam_doctor_code examDoctorsCode,exam_doctor_name examDoctorsName
        ,ord_exec_doctor_code ordExecDoctorCode, ord_exec_doctor_name ordExecDoctorName
        ,consultants_code consultantsCode,consultants_name consultantsName
        ,h.recorders_code recordersCode,h.recorders_name recordersName,h.report_date reportTime,h.outp_no outpNo
        ,h.regist_time registTime,h.regist_user_code regu__userName,regist_user_name regu__nickName
        ,h.report_url_pdf reportUrlPdf,h.report_url_org_pdf reportUrlOrgPdf,h.report_url_jpg
        reportUrlJpg,h.report_url_username reportUrlUsername,h.report_url_password reportUrlPassword
        ,h.report_no reportNo,h.image_no imageNo,h.status_of_send_report statusOfSendReport, h.exam_batch_no
        examBatchNo, h.exam_serial_no examSerialNo, h.accession_number accessionNumber
        ,p.id p__id,p.patient_id p__patientId,p.name p__name,p.name_pingyin p__namePingyin,p.age p__age,p.reg_no
        p__registNo,p.birthday p__birthday
        ,pg.dict_label P__gd__dictLabel,p.gender_code P__gd__dictValue
        ,pau.dict_label P__ageu__dictLabel,p.age_unit_code P__ageu__dictValue

        ,ci.id ci__id,ci.call_no ci__callNo,cer.room_code ci__rm__roomCode,cer.room_name ci__rm__roomName

        <!-- ,dcs.id dcs__id,dcs.studyInstanceUid dcs__studyInstanceUid,dcs.dicomStudyInstanceUid dcs__dicomStudyInstanceUid -->

        ,rt.dict_code rt__dictCode,rt.dict_value rt__dictValue,rt.dict_label rt__dictLabel
        ,rts.dict_code rts__dictCode,rts.dict_value rts__dictValue,rts.dict_label rts__dictLabel
        ,ei.dict_code ei__dictCode,ei.dict_value ei__dictValue,ei.dict_label ei__dictLabel
        ,er.room_code er__roomCode,er.room_name er__roomName
        ,wd.dict_code wd__dictCode,wd.dict_value wd__dictValue,wd.dict_label wd__dictLabel
        ,rm.dict_code rm__dictCode,rm.dict_value rm__dictValue,rm.dict_label rm__dictLabel
        ,cos.dict_code cos__dictCode,cos.dict_value cos__dictValue,cos.dict_label cos__dictLabel
        ,res2.dict_code res2__dictCode,res2.dict_value res2__dictValue,res2.dict_label res2__dictLabel
        ,erp.dict_code erp__dictCode,erp.dict_value erp__dictValue,erp.dict_label erp__dictLabel
        ,cp.dict_code cp__dictCode,cp.dict_value cp__dictValue,cp.dict_label cp__dictLabel
        ,org.dict_code org__dictCode,org.dict_value org__dictValue,org.dict_label org__dictLabel
        ,org.extend_i1 org__ext__extendI1

        ,h.regist_user_code reg__userName,h.regist_user_name reg__nickName
        ,h.exam_doctor_code eu__userName,h.exam_doctor_name eu__nickName
        <!-- ,eu.user_id eu__userId,eu.user_name eu__userName,eu.nick_name eu__nickName -->
        <!--存什么读什么,ru.user_id ru__userId,ru.user_name ru__userName,ifnull(h.req_doctor_name,ru.nick_name) ru__nickName
        ,aud.user_id aud__userId,aud.user_name aud__userName,ifnull(h.audit_doctor_name,aud.nick_name) aud__nickName
        ,aud2.user_id aud2__userId,aud2.user_name aud2__userName,ifnull(h.reaudit_doctor_name,aud2.nick_name) aud2__nickName
        ,sig.user_id sig__userId,sig.user_name sig__userName,ifnull(h.sign_doctor_name,sig.nick_name) sig__nickName
        ,rpu.user_id rpu__userId,rpu.user_name rpu__userName,ifnull(h.report_doctor_name,rpu.nick_name) rpu__nickName -->
        ,h.req_doctor_code ru__userName,h.req_doctor_name ru__nickName
        ,h.audit_doctor_code aud__userName,h.audit_doctor_name aud__nickName
        ,h.second_audit_doctor_code secondaud__userName,h.second_audit_doctor_name secondaud__nickName
        ,h.third_audit_doctor_code thirdaud__userName,h.third_audit_doctor_name thirdaud__nickName
        ,h.reaudit_doctor_code aud2__userName,h.reaudit_doctor_name aud2__nickName
        ,h.sign_doctor_code sig__userName,h.sign_doctor_name sig__nickName
        ,h.report_doctor_code rpu__userName,h.report_doctor_name rpu__nickName
        ,h.oper_doctor_code od__userName,h.oper_doctor_name od__nickName
        ,h.apply_id applyId

        <!-- ,rd.dept_id rd__deptId,rd.dept_name rd__deptName -->
        ,h.req_dept_code rd__deptCode,h.req_dept_name rd__deptName
        ,ex.dept_id ex__deptId,ex.dept_name ex__deptName

        ,exdev.id exdev__id,exdev.modality_code exdev__modalityCode,exdev.device_code exdev__deviceCode

        <!-- 检查单 -->
        from `d_exam_info` `h`
        <!-- 患者信息 -->
        join d_patient p on p.patient_id=h.patient_id
        <!-- 排队信息 -->
        left join d_call_info ci on ci.exam_info_id=h.id
        left join d_equip_room cer on cer.room_code=ci.call_room_code
        <!-- dicom
        left join d_dicom_study dcs on dcs.exam_info_id=h.id -->
        <!-- -->
        left join v_sys_dict_data rt on rt.dict_type='uis_exam_modality' and rt.dict_value=h.exam_modality_code
        left join v_sys_dict_data rts on rts.dict_type='uis_inp_type' and rts.dict_value=h.inp_type_code
        left join v_sys_dict_data ei on ei.dict_type='uis_exam_item' and ei.dict_value=h.exam_item_code
        left join d_equip_room er on er.room_code=h.equip_room_code
        left join v_sys_dict_data wd on wd.dict_type='uis_inp_ward' and wd.dict_value=h.inp_ward_code
        left join v_sys_dict_data rm on rm.dict_type='uis_inp_room' and rm.dict_value=h.inp_room_code
        left join v_sys_dict_data cos on cos.dict_type='uis_exam_cost_type' and cos.dict_value=h.exam_cost_type_code
        left join v_sys_dict_data res2 on res2.dict_type='uis_exam_result_status' and
        res2.dict_value=h.result_status_code
        left join v_sys_dict_data erp on erp.dict_type='uis_exam_result_prop' and erp.dict_value=h.exam_result_prop_code
        left join v_sys_dict_data pg on pg.dict_type='uis_gender_type' and pg.dict_value=p.gender_code
        left join v_sys_dict_data pau on pau.dict_type='uis_age_unit' and pau.dict_value=p.age_unit_code
        left join v_sys_dict_data cp on cp.dict_type='uis_cond_parting' and cp.dict_value=h.cond_parting_code
        left join v_sys_dict_data org on org.dict_type='common_organization_code' and
        org.dict_value=h.inspection_org_code

        <!-- left join v_sys_user eu on eu.user_name=h.exam_doctor_code -->
        <!--存什么读什么left join v_sys_user ru on ru.user_name=h.req_doctor_code
        left join v_sys_user aud on aud.user_name=h.audit_doctor_code
        left join v_sys_user aud2 on aud2.user_name=h.reaudit_doctor_code
        left join v_sys_user sig on sig.user_name=h.sign_doctor_code
        left join v_sys_user rpu on rpu.user_name=h.report_doctor_code -->

        <!-- left join v_sys_dept rd on rd.dept_id=h.req_dept_code -->
        left join v_sys_dept ex on ex.dept_id=h.exam_dept_code

        left join d_dicom_info exdev on exdev.modality_code=h.modality_code
    </sql>

    <sql id="selectWhereKey">
        where 1=1
        <if test="null!=id"> and `h`.`id`=#{id}</if>
        <if test="null!=examUid and ''!=examUid"> and `h`.`exam_uid`=#{examUid}</if>
        <if test="null!=reportNo and ''!=reportNo"> and `h`.`report_no`=#{reportNo}</if>
        <if test="null!=imageNo and ''!=imageNo"> and `h`.`image_no`=#{imageNo}</if>
        <if test="null!=examDept and ''!=examDept and null!=examDept.deptId and ''!=examDept.deptId">and concat(',',h.exam_dept_code,',') like concat('%,',#{examDept.deptId},',%')</if>
        <if test="null!=params and null!=params.dataScope">${params.dataScope}</if>
    </sql>

    <select id="selectOne" resultMap="resultMapFull">
        <include refid="selectBase"/>
        <include refid="selectWhereKey"/>
        <if test="null==id and (null==examUid or ''==examUid)"> and 1=0</if>
    </select>

    <select id="selectList" parameterType="yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo" resultMap="resultMapOutline">
        <include refid="selectBase"/>
        <include refid="selectWhereKey"/>

        <if test="null!=examNo and ''!=examNo"> and `h`.`exam_no`=#{examNo}</if>
        <!-- 检查年龄 -->
        <if test="null!=examAge and ''!=examAge"> and `h`.`exam_age`=#{examAge}</if>
        <!-- 检查类型 -->
        <if test="null!=examModality and null!=examModality.dictValue and ''!=examModality.dictValue"> and h.exam_modality_code=#{examModality.dictValue}</if>
        <if test="null!=examModalitiesCodes">
            <foreach collection="examModalitiesCodes" item="var" open=" and h.exam_modality_code in (" close=")" separator=",">
                #{var}
            </foreach>
        </if>
        <!-- 患者信息 -->
        <if test="null!=patientInfo">
            <if test="null!=patientInfo.name and ''!=patientInfo.name"> and `p`.`name` like replace(#{patientInfo.name},'*','%')</if>
            <if test="null!=patientInfo.medicalRecordNo and ''!=patientInfo.medicalRecordNo"> and `p`.`medical_record_no`=#{patientInfo.medicalRecordNo}</if>
            <if test="null!=patientInfo.registNo and ''!=patientInfo.registNo">
                and (`p`.`reg_no`=#{patientInfo.registNo}
                <if test="null != inpNo and '' != inpNo">
                    <if test="orMatchType">
                        or h.inp_no=#{inpNo}
                    </if>
                    <if test="!orMatchType">
                        and h.inp_no=#{inpNo}
                    </if>
                </if>
                )
            </if>
            <if test="null!=patientInfo.namePingyin and ''!=patientInfo.namePingyin"> and `p`.`name_pingyin`=#{patientInfo.namePingyin}</if>
            <if test="null!=patientInfo.gender and null!=patientInfo.gender.dictValue and ''!=patientInfo.gender.dictValue"> and `p`.`gender_code`=#{patientInfo.gender.dictValue}</if>
            <if test="null!=patientInfo.birthday"> and `p`.`birthday`=#{patientInfo.birthday}</if>
        </if>
        <!-- 检查项目 -->
        <if test="null!=examItemCodes">
            <foreach collection="examItemCodes" item="var" open=" and h.exam_item_code in (" close=")" separator=",">
                #{var}
            </foreach>
        </if>
        <!-- 诊前准备 -->
        <if test="null!=examPrerequire"> and h.exam_prerequire=#{examPrerequire}</if>
        <!-- 下午检查 -->
        <if test="null!=examAtPm"> and h.exam_at_pm=#{examAtPm}</if>
        <!-- 排除诊前准备 -->
        <if test="null!=examPrerequireExclude and examPrerequireExclude"> and (h.exam_prerequire is null or h.exam_prerequire=1)</if>
        <!--  -->
        <if test="0==status"> and  h.status=0</if>
        <!-- 申请医生 -->
        <if test="null!=reqDoctor">
            <if test="null!=reqDoctor.userName and ''!=reqDoctor.userName"> and h.req_doctor_code=#{reqDoctor.userName}</if>
        </if>
        <!-- 检查医生 -->
        <if test="null!=examDoctor">
            <if test="null!=examDoctor.nickName and ''!=examDoctor.nickName"> and h.exam_doctor_name like replace(#{examDoctor.nickName},'*','%')</if>
            <if test="null!=examDoctor.userName and ''!=examDoctor.userName"> and h.exam_doctor_code=#{examDoctor.userName}</if>
        </if>
        <if test="null!=examDoctorUserNames">
            <foreach collection="examDoctorUserNames" item="var" open=" and (" close=")" separator=" or ">concat(',',h.exam_doctor_code,',') like concat('%,',#{var},',%')</foreach>
        </if>
        <!-- 工作状态 -->
        <if test="null!=resultStatus and null!=resultStatus.dictValue and ''!=resultStatus.dictValue">
            and h.result_status_code=#{resultStatus.dictValue}
        </if>
        <if test="null != resultStatusValues or (null!=resultStatusAsStatus and ''!=resultStatusAsStatus)">
            and(
            <trim prefixOverrides="or">
                <if test="null != resultStatusValues">
                    <foreach collection="resultStatusValues" item="var" open=" or h.result_status_code in (" close=")" separator=",">#{var}</foreach>
                </if>
                <if test="(null!=resultStatusAsStatus and ''!=resultStatusAsStatus)"> or h.status=#{resultStatusAsStatus}</if>
            </trim>
            )
        </if>
        <!-- 登记时间 -->
<!--        <if test="null!=createTimeGe"> and (h.create_time&gt;=#{createTimeGe} and h.appoint_time is null or h.appoint_time&gt;=#{createTimeGe})</if>-->
<!--        <if test="null!=createTimeLt"> and (h.create_time&lt;date_add(#{createTimeLt},INTERVAL 1 DAY) and h.appoint_time is null or h.appoint_time&lt;date_add(#{createTimeLt},INTERVAL 1 DAY))</if>-->
        <if test="null!=createTimeGe"> and (h.create_time&gt;=#{createTimeGe})</if>
        <if test="null!=createTimeLt"> and (h.create_time&lt;date_add(#{createTimeLt},INTERVAL 1 DAY))</if>
        <!-- 预约时间 -->
        <if test="null!=appointTimeGe"> and h.appoint_time&gt;=#{appointTimeGe}</if>
        <if test="null!=appointTimeLt"> and h.appoint_time&lt;date_add(#{appointTimeLt},INTERVAL 1 DAY)</if>
        <!-- 当日检查的：当日登记+预约当日
        <if test="appointWithCreated"> and(h.create_time&gt;=current_date() and h.appoint_time is null
            or h.appoint_time&gt;=current_date() and h.appoint_time&lt;date_add(current_date(), interval 1 day))</if> -->
        <!-- 检查时间 -->
        <if test="null!=examTimeGe"> and h.exam_time&gt;=#{examTimeGe}</if>
        <if test="null!=examTimeLt"> and h.exam_time&lt;date_add(#{examTimeLt},INTERVAL 1 DAY)</if>
        <!-- 审核时间 -->
        <if test="null!=auditTimeGe"> and h.audit_time&gt;=#{auditTimeGe}</if>
        <if test="null!=auditTimeLt"> and h.audit_time&lt;date_add(#{auditTimeLt},INTERVAL 1 DAY)</if>
        <!-- 检查机房 -->
        <if test="null!=callInfo and null!=callInfo.callRoom and null!=callInfo.callRoom.roomCode and ''!=callInfo.callRoom.roomCode">
            and ci.call_room_code=#{callInfo.callRoom.roomCode}
        </if>
        <if test="null!=equipRoomsCode and equipRoomsCode.size()>0">
            <foreach collection="equipRoomsCode" item="var" open=" and ci.call_room_code in (" close=")" separator=",">#{var}</foreach>
        </if>
        <!-- 设备型号 -->
        <if test="null!=examDevicesCode and ''!=examDevicesCode">
            and cer.room_code in (select equip_room_code from r_dicominfo_equip_room rde,d_dicom_info dcmi where rde.equip_code=dcmi.id and dcmi.device_code in(
            <foreach collection="examDevicesCode" item="var" separator=",">#{var}</foreach>
            ))
        </if>
        <!-- 住院号 -->
        <if test="!(null!=patientInfo and null!=patientInfo.registNo and ''!=patientInfo.registNo)">
            <if test="null!=inpNo and ''!=inpNo">
                and h.inp_no=#{inpNo}
            </if>
        </if>
        <!-- 报告医生 -->
        <if test="null!=reportDoctor">
            <if test="null!=reportDoctor.nickName and ''!=reportDoctor.nickName"> and h.report_doctor_name like replace(#{reportDoctor.nickName},'*','%')</if>
            <if test="null!=reportDoctor.userName and ''!=reportDoctor.userName"> and h.report_doctor_code=#{reportDoctor.userName}</if>
        </if>
        <if test="null!=reportDoctorUserNames">
            <foreach collection="reportDoctorUserNames" item="var" open=" and (" close=")" separator=" or ">concat(',',h.report_doctor_code,',') like concat('%,',#{var},',%')</foreach>
        </if>
        <!-- 审核医生 -->
        <if test="null!=auditDoctor">
            <if test="null!=auditDoctor.nickName and ''!=auditDoctor.nickName"> and h.audit_doctor_name like replace(#{auditDoctor.nickName},'*','%')</if>
            <if test="null!=auditDoctor.userName and ''!=auditDoctor.userName"> and h.audit_doctor_code=#{auditDoctor.userName}</if>
        </if>
        <!-- 操作者 -->
        <if test="null!=operDoctor">
            <if test="null!=operDoctor.nickName and ''!=operDoctor.nickName"> and h.oper_doctor_name like replace(#{operDoctor.nickName},'*','%')</if>
            <if test="null!=operDoctor.userName and ''!=operDoctor.userName"> and h.oper_doctor_code=#{operDoctor.userName}</if>
        </if>
        <!-- 检查所见 -->
        <if test='null != examDescSegm and examDescSegm.length > 0'>
            and (
            <foreach collection="examDescSegm" item="item" open="" separator=" and " close="">
                h.exam_conclusion like '%${item}%'
            </foreach>
            )
        </if>
        <!-- 检查诊断 -->
        <if test='null != examDiagnosisSegm and examDiagnosisSegm.length > 0'>
            and (
            <foreach collection="examDiagnosisSegm" item="item" open="" separator=" and " close="">
                h.exam_diagnosis like '%${item}%'
            </foreach>
            )
        </if>
        <!-- 就诊类型 -->
        <if test="null!=inpType and null!=inpType.dictValue and ''!=inpType.dictValue">
            and h.inp_type_code=#{inpType.dictValue}
        </if>

        <!-- 阴阳性 -->
        <if test="null!=examResultProp and null!=examResultProp.dictValue and ''!=examResultProp.dictValue">
            and h.exam_result_prop_code=#{examResultProp.dictValue}
        </if>

        <if test="null!=inpTypeValues and inpTypeValues.length>0">
            <foreach collection="inpTypeValues" item="item" open=" and h.inp_type_code in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <!-- 状态 -->
        <if test="null!=status"> and h.status=#{status}</if>
        <!-- 就诊号 -->
        <if test="null!=outpNo and ''!=outpNo"> and h.outp_no=#{outpNo}</if>
        <!-- -->
        <if test="null!=statusOfSendReport"> and h.status_of_send_report=#{statusOfSendReport}</if>
        <!-- 部门 -->
        <if test="null!=examDept and ''!=examDept and null!=examDept.deptId and ''!=examDept.deptId"> and h.exam_dept_code=#{examDept.deptId}</if>
        <if test="null!=examDeptsCode and ''!=examDeptsCode">
            <foreach collection="examDeptsCode" item="item" open=" and h.exam_dept_code in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by <if test="null!=orderBy and ''!=orderBy">${orderBy},</if>h.id desc
    </select>
    
    <select id="selectListFull" parameterType="yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo" resultMap="resultMapFull">
        <include refid="selectBase"/>
        <include refid="selectWhereKey"/>

        <if test="null!=examNo and ''!=examNo"> and `h`.`exam_no`=#{examNo}</if>
        <!--和下面的重复了 <if test="null!=inpNo and ''!=inpNo"> and `h`.`inp_no`=#{inpNo}</if>-->
        <!-- 检查类型 -->
        <if test="null!=examModality and null!=examModality.dictValue and ''!=examModality.dictValue"> and h.exam_modality_code=#{examModality.dictValue}</if>
        <if test="null!=examModalitiesCodes">
            <foreach collection="examModalitiesCodes" item="var" open=" and h.exam_modality_code in (" close=")" separator=",">
                #{var}
            </foreach>
        </if>
        <!-- 患者信息 -->
        <if test="null!=patientInfo">
            <if test="null!=patientInfo.name and ''!=patientInfo.name"> and `p`.`name` like replace(#{patientInfo.name},'*','%')</if>
            <if test="null!=patientInfo.medicalRecordNo and ''!=patientInfo.medicalRecordNo"> and `p`.`medical_record_no`=#{patientInfo.medicalRecordNo}</if>
            <if test="null!=patientInfo.registNo and ''!=patientInfo.registNo">
                and (`p`.`reg_no`=#{patientInfo.registNo}
                <if test="null != inpNo and '' != inpNo">
                    <if test="orMatchType">
                        or h.inp_no=#{inpNo}
                    </if>
                    <if test="!orMatchType">
                        and h.inp_no=#{inpNo}
                    </if>
                </if>
                )
            </if>
            <if test="null!=patientInfo.namePingyin and ''!=patientInfo.namePingyin"> and `p`.`name_pingyin`=#{patientInfo.namePingyin}</if>
            <if test="null!=patientInfo.gender and null!=patientInfo.gender.dictValue and ''!=patientInfo.gender.dictValue"> and `p`.`gender_code`=#{patientInfo.gender.dictValue}</if>
            <if test="null!=patientInfo.birthday"> and `p`.`birthday`=#{patientInfo.birthday}</if>
        </if>
        <!-- 检查项目 -->
        <if test="null!=examItemCodes">
            <foreach collection="examItemCodes" item="var" open=" and h.exam_item_code in (" close=")" separator=",">
                #{var}
            </foreach>
        </if>
        <!-- 诊前准备 -->
        <if test="null!=examPrerequire"> and h.exam_prerequire=#{examPrerequire}</if>
        <!-- 下午检查 -->
        <if test="null!=examAtPm"> and h.exam_at_pm=#{examAtPm}</if>
        <!-- 排除诊前准备 -->
        <if test="null!=examPrerequireExclude and examPrerequireExclude"> and (h.exam_prerequire is null or h.exam_prerequire=1)</if>
        <!--  -->
        <if test="0==status"> and  h.status=0</if>
        <!-- 检查医生 -->
        <if test="null!=examDoctor">
            <if test="null!=examDoctor.nickName and ''!=examDoctor.nickName"> and h.exam_doctor_name like replace(#{examDoctor.nickName},'*','%')</if>
            <if test="null!=examDoctor.userName and ''!=examDoctor.userName"> and h.exam_doctor_code=#{examDoctor.userName}</if>
        </if>
        <if test="null!=examDoctorUserNames">
            <foreach collection="examDoctorUserNames" item="var" open=" and (" close=")" separator=" or ">concat(',',h.exam_doctor_code,',') like concat('%,',#{var},',%')</foreach>
        </if>
        <!-- 工作状态 -->
        <if test="null!=resultStatus and null!=resultStatus.dictValue and ''!=resultStatus.dictValue">
            and h.result_status_code=#{resultStatus.dictValue}
        </if>
        <if test="null != resultStatusValues or (null!=resultStatusAsStatus and ''!=resultStatusAsStatus)">
            and(
            <trim prefixOverrides="or">
            <if test="null != resultStatusValues">
            <foreach collection="resultStatusValues" item="var" open=" or h.result_status_code in (" close=")" separator=",">#{var}</foreach>
            </if>
            <if test="(null!=resultStatusAsStatus and ''!=resultStatusAsStatus)"> or h.status=#{resultStatusAsStatus}</if>
            </trim>
            )
        </if>
        <!-- 登记时间 -->
        <if test="null!=createTimeGe"> and (h.create_time&gt;=#{createTimeGe} )</if>
        <if test="null!=createTimeLt"> and (h.create_time&lt;date_add(#{createTimeLt},INTERVAL 1 DAY))</if>
        <!-- 预约时间 -->
        <if test="null!=appointTimeGe"> and h.appoint_time&gt;=#{appointTimeGe}</if>
        <if test="null!=appointTimeLt"> and h.appoint_time&lt;date_add(#{appointTimeLt},INTERVAL 1 DAY)</if>
        <!-- 当日检查的：当日登记+预约当日
        <if test="appointWithCreated"> and(h.create_time&gt;=current_date() and h.appoint_time is null
            or h.appoint_time&gt;=current_date() and h.appoint_time&lt;date_add(current_date(), interval 1 day))</if> -->
        <!-- 检查时间 -->
        <if test="null!=examTimeGe"> and h.exam_time&gt;=#{examTimeGe}</if>
        <if test="null!=examTimeLt"> and h.exam_time&lt;date_add(#{examTimeLt},INTERVAL 1 DAY)</if>
        <!-- 审核时间 -->
        <if test="null!=auditTimeGe"> and h.audit_time&gt;=#{auditTimeGe}</if>
        <if test="null!=auditTimeLt"> and h.audit_time&lt;date_add(#{auditTimeLt},INTERVAL 1 DAY)</if>
        <!-- 检查机房 -->
        <if test="null!=callInfo and null!=callInfo.callRoom and null!=callInfo.callRoom.roomCode and ''!=callInfo.callRoom.roomCode">
            and ci.call_room_code=#{callInfo.callRoom.roomCode}
        </if>
        <if test="null!=equipRoomsCode and equipRoomsCode.size()>0">
            <foreach collection="equipRoomsCode" item="var" open=" and ci.call_room_code in (" close=")" separator=",">#{var}</foreach>
        </if>
        <!-- 设备型号 -->
        <if test="null!=examDevicesCode and ''!=examDevicesCode">
            and cer.room_code in (select equip_room_code from r_dicominfo_equip_room rde,d_dicom_info dcmi where rde.equip_code=dcmi.id and dcmi.device_code in(
            <foreach collection="examDevicesCode" item="var" separator=",">#{var}</foreach>
            ))
        </if>
        <!-- 住院号 -->
        <if test="!(null!=patientInfo and null!=patientInfo.registNo and ''!=patientInfo.registNo)">
            <if test="null!=inpNo and ''!=inpNo">
                and h.inp_no=#{inpNo}
            </if>
        </if>
        <!-- 报告医生 -->
        <if test="null!=reportDoctor">
            <if test="null!=reportDoctor.nickName and ''!=reportDoctor.nickName"> and h.report_doctor_name like replace(#{reportDoctor.nickName},'*','%')</if>
            <if test="null!=reportDoctor.userName and ''!=reportDoctor.userName"> and h.report_doctor_code=#{reportDoctor.userName}</if>
        </if>
        <if test="null!=reportDoctorUserNames">
            <foreach collection="reportDoctorUserNames" item="var" open=" and (" close=")" separator=" or ">concat(',',h.report_doctor_code,',') like concat('%,',#{var},',%')</foreach>
        </if>
        <!-- 审核医生 -->
        <if test="null!=auditDoctor">
            <if test="null!=auditDoctor.nickName and ''!=auditDoctor.nickName"> and h.audit_doctor_name like replace(#{auditDoctor.nickName},'*','%')</if>
            <if test="null!=auditDoctor.userName and ''!=auditDoctor.userName"> and h.audit_doctor_code=#{auditDoctor.userName}</if>
        </if>
        <!-- 操作者 -->
        <if test="null!=operDoctor">
            <if test="null!=operDoctor.nickName and ''!=operDoctor.nickName"> and h.oper_doctor_name like replace(#{operDoctor.nickName},'*','%')</if>
            <if test="null!=operDoctor.userName and ''!=operDoctor.userName"> and h.oper_doctor_code=#{operDoctor.userName}</if>
        </if>
        <!-- 检查所见 -->
        <if test='null != examDescSegm and examDescSegm.length > 0'>
            and (
            <foreach collection="examDescSegm" item="item" open="" separator=" and " close="">
                h.exam_conclusion like '%${item}%'
            </foreach>
            )
        </if>
        <!-- 检查诊断 -->
        <if test='null != examDiagnosisSegm and examDiagnosisSegm.length > 0'>
            and (
            <foreach collection="examDiagnosisSegm" item="item" open="" separator=" and " close="">
                h.exam_diagnosis like '%${item}%'
            </foreach>
            )
        </if>
        <!-- 就诊类型 -->
        <if test="null!=inpType and null!=inpType.dictValue and ''!=inpType.dictValue">
            and h.inp_type_code=#{inpType.dictValue}
        </if>

        <!-- 阴阳性 -->
        <if test="null!=examResultProp and null!=examResultProp.dictValue and ''!=examResultProp.dictValue">
            and h.exam_result_prop_code=#{examResultProp.dictValue}
        </if>

        <if test="null!=inpTypeValues and inpTypeValues.length>0">
        <foreach collection="inpTypeValues" item="item" open=" and h.inp_type_code in (" close=")" separator=",">
          #{item}
        </foreach>
        </if>
        <!-- 状态 -->
        <if test="null!=status"> and h.status=#{status}</if>
        <!-- 就诊号 -->
        <if test="null!=outpNo and ''!=outpNo"> and h.outp_no=#{outpNo}</if>
        <!-- -->
        <if test="null!=statusOfSendReport"> and h.status_of_send_report=#{statusOfSendReport}</if>
        <!-- 部门 -->
        <if test="null!=examDept and ''!=examDept and null!=examDept.deptId and ''!=examDept.deptId"> and h.exam_dept_code=#{examDept.deptId}</if>
        <if test="null!=examDeptsCode and ''!=examDeptsCode">
            <foreach collection="examDeptsCode" item="item" open=" and h.exam_dept_code in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by <if test="null!=orderBy and ''!=orderBy">${orderBy},</if>h.id desc
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into `d_exam_info` (regist_way,exam_uid,exam_no,exam_batch_no, exam_serial_no, accession_number,exam_age,apply_id
        , patient_id
        , exam_modality_code
        , inp_type_code
        , exam_item_code
        , equip_room_code
        , exam_parts_id
        , exam_dept_code, exam_dept_name
        , exam_doctor_code, exam_doctor_name
        , oper_doctor_code,oper_doctor_name
        , req_dept_code, req_dept_name
        , req_doctor_code, req_doctor_name
        , inp_ward_code,inp_room_code,exam_cost_type_code,cond_parting_code
        , exam_cost, green_channel_flag, appoint_time, clinic_diagnosis, allergy_history, clinic_disease,adm_no
        , operation_info,adm_series_num,inp_no,bed_no,apply_path,ord_id,unexecute_ord_id,ord_name,arcim_code,ord_bill_status
        , ord_priority_code,ord_priority,exam_purpose,is_emergency,ord_status,req_time,exam_time,result_status_code
        , exam_prerequire, reserved_no_used, exam_at_pm, note_info,origin_id,inp_times
        , regist_user_code,regist_user_name,regist_time
        ,modality_code,device_code,report_no,outp_no,image_no,sign_info_uuid
        ,inspection_org_code
        ,ord_exec_doctor_code, ord_exec_doctor_name
        )
        values(#{registWay},#{examUid},#{examNo},#{examBatchNo},#{examSerialNo},#{AccessionNumber},#{examAge},#{applyId}
        ,<choose><when test="null!=patientInfo">#{patientInfo.patientId}</when><otherwise>null</otherwise></choose>
        ,<choose><when test="null!=examModality">#{examModality.dictValue}</when><otherwise>null</otherwise></choose>
        ,<choose><when test="null!=inpType">#{inpType.dictValue}</when><otherwise>null</otherwise></choose>
        ,<choose><when test="null!=examItem">#{examItem.dictValue}</when><otherwise>null</otherwise></choose>
        ,<choose><when test="null!=equipRoom">#{equipRoom.roomCode}</when><otherwise>null</otherwise></choose>
        ,concat(<choose><when test="null!=examParts and examParts.size>0"><foreach collection="examParts" item="var" separator=",',',">#{var.id}</foreach></when><otherwise>null</otherwise></choose>)
        ,<choose><when test="null!=examDept">#{examDept.deptId},#{examDept.deptName}</when><otherwise>null,null</otherwise></choose>
        <!-- ,<choose><when test="null!=examDoctor">#{examDoctor.userName},#{examDoctor.nickName}</when><otherwise>null,null</otherwise></choose> -->
        ,#{examDoctorsCode},#{examDoctorsName}
        ,<choose><when test="null!=operDoctor">#{operDoctor.userName}#{operDoctor.nickName}</when><otherwise>null,null</otherwise></choose>
        ,<choose><when test="null!=reqDept">#{reqDept.deptCode},#{reqDept.deptName}</when><otherwise>null,null</otherwise></choose>
        ,<choose><when test="null!=reqDoctor">#{reqDoctor.userName},#{reqDoctor.nickName}</when><otherwise>null,null</otherwise></choose>
        ,<choose><when test="null!=inpWard">#{inpWard.dictValue}</when><otherwise>null</otherwise></choose>
        ,<choose><when test="null!=inpRoom">#{inpRoom.dictValue}</when><otherwise>null</otherwise></choose>
        ,<choose><when test="null!=examCostType">#{examCostType.dictValue}</when><otherwise>null</otherwise></choose>
        ,<choose><when test="null!=condParting">#{condParting.dictValue}</when><otherwise>null</otherwise></choose>
        ,#{examCost}, #{greenChannelFlag}, #{appointTime}, #{clinicDiagnosis}, #{allergyHistory}, #{clinicDisease},#{admNo}
        , #{operationInfo},#{admSeriesNum}, #{inpNo},#{bedNo},#{applyPath},#{ordId},#{unexecuteOrdId},#{ordName},#{arcimCode},#{ordBillStatus}
        ,#{ordPriorityCode},#{ordPriority},#{examPurpose},#{emergency},#{ordStatus},#{reqTime},#{examTime},0
        , #{examPrerequire}, #{reservedNoUsed}, #{examAtPm}, #{noteInfo},#{originId},#{inpTimes}
        , <choose><when test="null!=creator">#{creator.userName},#{creator.nickName}</when><otherwise>null,null</otherwise></choose>,now()
        ,<choose><when test="null!=examDevice">#{examDevice.modalityCode},#{examDevice.deviceCode}</when><otherwise>null,null</otherwise></choose>
        ,<choose><when test="null!=reportNo">#{reportNo}</when><otherwise>null</otherwise></choose>
        ,<choose><when test="null!=outpNo">#{outpNo}</when><otherwise>null</otherwise></choose>
        ,<choose><when test="null!=imageNo">#{imageNo}</when><otherwise>null</otherwise></choose>
        ,<choose><when test="null!=signInfo">#{signInfo.uuid}</when><otherwise>null</otherwise></choose>
        ,#{inspectionOrg.dictValue}
        ,#{ordExecDoctorCode}, #{ordExecDoctorName}
        )
    </insert>
	 
    <update id="update">
        update `d_exam_info`
        set exam_no=#{examNo}
        ,patient_id=<choose><when test="null!=patientInfo">#{patientInfo.patientId}</when><otherwise>null</otherwise></choose>
        ,`exam_modality_code`=<choose><when test="null!=examModality">#{examModality.dictValue}</when><otherwise>null</otherwise></choose>
        ,`inp_type_code`=<choose><when test="null!=inpType">#{inpType.dictValue}</when><otherwise>null</otherwise></choose>
        ,`exam_item_code`=<choose><when test="null!=examItem">#{examItem.dictValue}</when><otherwise>null</otherwise></choose>
        ,`equip_room_code`=<choose><when test="null!=equipRoom">#{equipRoom.roomCode}</when><otherwise>null</otherwise></choose>
        ,`exam_parts_id`=concat(<choose><when test="null!=examParts and examParts.size>0"><foreach collection="examParts" item="var" separator=",',',">#{var.id}</foreach></when><otherwise>null</otherwise></choose>)
        ,exam_dept_code=<choose><when test="null!=examDept">#{examDept.deptId}</when><otherwise>null</otherwise></choose>
        ,exam_dept_name=<choose><when test="null!=examDept">#{examDept.deptName}</when><otherwise>null</otherwise></choose>
        <!-- ,exam_doctor_code=<choose><when test="null!=examDoctor">#{examDoctor.userName}</when><otherwise>null</otherwise></choose>
        ,exam_doctor_name=<choose><when test="null!=examDoctor">#{examDoctor.nickName}</when><otherwise>null</otherwise></choose> -->
        ,exam_doctor_code=#{examDoctorsCode},exam_doctor_name=#{examDoctorsName}
        ,req_dept_code=<choose><when test="null!=reqDept">#{reqDept.deptCode}</when><otherwise>null</otherwise></choose>
        ,req_dept_name=<choose><when test="null!=reqDept">#{reqDept.deptName}</when><otherwise>null</otherwise></choose>
        ,req_doctor_code=<choose><when test="null!=reqDoctor">#{reqDoctor.userName}</when><otherwise>null</otherwise></choose>
        ,req_doctor_name=<choose><when test="null!=reqDoctor">#{reqDoctor.nickName}</when><otherwise>null</otherwise></choose>
        ,inp_ward_code=<choose><when test="null!=inpWard">#{inpWard.dictValue}</when><otherwise>null</otherwise></choose>
        ,inp_room_code=<choose><when test="null!=inpRoom">#{inpRoom.dictValue}</when><otherwise>null</otherwise></choose>
        ,exam_cost_type_code=<choose><when test="null!=examCostType">#{examCostType.dictValue}</when><otherwise>null</otherwise></choose>
        ,cond_parting_code=<choose><when test="null!=condParting">#{condParting.dictValue}</when><otherwise>null</otherwise></choose>
        ,exam_cost=#{examCost}
        , green_channel_flag=#{greenChannelFlag}
        , appoint_time=#{appointTime}
        , clinic_diagnosis=#{clinicDiagnosis}
        , allergy_history=#{allergyHistory}
        , clinic_disease=#{clinicDisease}
        , adm_no=#{admNo}
        , operation_info=#{operationInfo}
        , adm_series_num=#{admSeriesNum}
        , inp_no=#{inpNo}
        , bed_no=#{bedNo}
        , apply_path=#{applyPath}
        , apply_id=#{applyId}
        ,ord_id=#{ordId},unexecute_ord_id=#{unexecuteOrdId},ord_name=#{ordName},arcim_code=#{arcimCode},ord_bill_status=#{ordBillStatus}
        ,ord_priority_code=#{ordPriorityCode},ord_priority=#{ordPriority},exam_purpose=#{examPurpose}
        ,is_emergency=#{emergency},ord_status=#{ordStatus},req_time=#{reqTime},exam_time=#{examTime}
        , exam_prerequire=#{examPrerequire}
        , reserved_no_used=#{reservedNoUsed}
        , exam_at_pm=#{examAtPm}
        , note_info=#{noteInfo}
        , inp_times=#{inpTimes}
        ,report_data_source=#{reportDataSource}
        ,report_url_pdf=#{reportUrlPdf}
        ,report_url_org_pdf=#{reportUrlOrgPdf}
        ,report_upload_filename=#{reportUploadFilename}
        ,report_no=#{reportNo}
        ,image_no=#{imageNo}
        ,report_date=#{reportTime}
        ,audit_time=#{auditTime}
        ,second_audit_time=#{secondAuditTime}
        ,third_audit_time=#{thirdAuditTime}
        ,sign_time=#{signTime}
        ,report_doctor_code=#{reportDoctor.userName},report_doctor_name=#{reportDoctor.nickName}
        ,audit_doctor_code=#{auditDoctor.userName},audit_doctor_name=#{auditDoctor.nickName}
        ,second_audit_doctor_code=#{secondAuditDoctor.userName},second_audit_doctor_name=#{secondAuditDoctor.nickName}
        ,third_audit_doctor_code=#{thirdAuditDoctor.userName},third_audit_doctor_name=#{thirdAuditDoctor.nickName}
        ,result_status_code=#{resultStatus.dictValue}
        ,exam_conclusion=#{examDesc}
        ,exam_diagnosis=#{examDiagnosis}
        ,operation_suggestion=#{operationSuggestion}
        ,exam_age=#{examAge}
        ,<choose><when test="null!=examResultProp and null!=examResultProp.dictValue and ''!=examResultProp.dictValue">exam_result_prop_code=#{examResultProp.dictValue}</when><otherwise>exam_result_prop_code=null</otherwise></choose>
        ,<choose><when test="null!=examDevice">modality_code=#{examDevice.modalityCode},device_code=#{examDevice.deviceCode}</when><otherwise>modality_code=null,device_code=null</otherwise></choose>
        ,<choose><when test="null!=operDoctor">oper_doctor_code=#{operDoctor.userName},oper_doctor_name=#{operDoctor.nickName}</when><otherwise>oper_doctor_code=null,oper_doctor_name=null</otherwise></choose>
        ,inspection_org_code=#{inspectionOrg.dictValue}
        ,ord_exec_doctor_code=#{ordExecDoctorCode}, ord_exec_doctor_name=#{ordExecDoctorName}
        where (result_status_code is null or result_status_code in('0','1','2')) and `id`=#{id}
    </update>
    <!-- 检查工作进度 -->
    <update id="updateResultStatus">
        update d_exam_info set result_status_code=#{resultStatus.dictValue}
        <!-- 更新为已检查 -->
        <if test='"1"==resultStatus.dictValue'>
            ,exam_prerequire=case when 0=exam_prerequire then 1 else exam_prerequire end
            ,exam_time=now()
            <if test="null!=examDoctor">
            ,exam_doctor_name=#{examDoctor.nickName}
            ,exam_doctor_code=#{examDoctor.userName}
            </if>
            <if test="null!=examDoctorsName">
                ,exam_doctor_name=#{examDoctorsName}
            </if>
            <if test="null!=examDoctorsCode">
                ,exam_doctor_code=#{examDoctorsCode}
            </if>
        </if>

        where id=#{id}
    </update>

    <!-- 上传报告后更新检查 -->
    <update id="uploadReportUpdateExam">
        update d_exam_info set id=#{id}
        <if test="null!=reportDataSource">
            ,report_data_source=#{reportDataSource}
        </if>
        <if test="null!=reportUrlPdf">
            ,report_url_pdf=#{reportUrlPdf}
        </if>
        <if test="null!=reportUrlOrgPdf">
            ,report_url_org_pdf=#{reportUrlOrgPdf}
        </if>
        <if test="null!=reportUploadFilename">
            ,report_upload_filename=#{reportUploadFilename}
        </if>
        <if test="null!=reportNo">
            ,report_no=#{reportNo}
        </if>
        <if test="null!=imageNo">
            ,image_no=#{imageNo}
        </if>
        <if test="null!=examTime">
            ,exam_time=#{examTime}
        </if>
        <if test="null!=reportTime">
            ,report_date=#{reportTime}
        </if>
        <if test="null!=auditTime">
            ,audit_time=#{auditTime}
        </if>
        <if test="null!=secondAuditTime">
            ,second_audit_time=#{secondAuditTime}
        </if>
        <if test="null!=thirdAuditTime">
            ,third_audit_time=#{thirdAuditTime}
        </if>
        <if test="null!=examDoctorsCode">
            ,exam_doctor_code=#{examDoctorsCode}
        </if>
        <if test="null!=examDoctorsName">
            ,exam_doctor_name=#{examDoctorsName}
        </if>
        <if test="null!=reportDoctor">
            ,report_doctor_code=#{reportDoctor.userName},report_doctor_name=#{reportDoctor.nickName}
        </if>
        <if test="null!=auditDoctor">
            ,audit_doctor_code=#{auditDoctor.userName},audit_doctor_name=#{auditDoctor.nickName}
        </if>
        <if test="null!=secondAuditDoctor">
            ,second_audit_doctor_code=#{secondAuditDoctor.userName},second_audit_doctor_name=#{secondAuditDoctor.nickName}
        </if>
        <if test="null!=thirdAuditDoctor">
            ,third_audit_doctor_code=#{thirdAuditDoctor.userName},third_audit_doctor_name=#{thirdAuditDoctor.nickName}
        </if>
        <if test="null!=resultStatus">
            ,result_status_code=#{resultStatus.dictValue}
        </if>

        where id=#{id}
    </update>

    <update id="updateDiagInfoByExamUid">
        update d_exam_info
        <set>
            <if test="null != examConclusion">
                exam_conclusion=#{examConclusion}
            </if>
            <if test="null != examDiagnosis">
                ,exam_diagnosis=#{examDiagnosis}
            </if>
            <if test="null != operationSuggestion">
                ,operation_suggestion=#{operationSuggestion}
            </if>
        </set>
        where exam_uid=#{examUid}
    </update>

    <!-- 报告处于登记完成、一检查和已报告时可保存报告 -->
    <update id="saveReport">
        update d_exam_info
        set exam_conclusion=#{examDesc}
        , exam_diagnosis=#{examDiagnosis}
        , operation_suggestion=#{operationSuggestion}
        , exam_result_prop_code=<choose><when test="null!=examResultProp">#{examResultProp.dictValue}</when><otherwise>null</otherwise></choose>
        , report_date=ifnull(report_date,now())
        <choose><when test="null!=reportDoctor">
        ,report_doctor_code=#{reportDoctor.userName},report_doctor_name=#{reportDoctor.nickName}
        </when><otherwise>,report_doctor_code=null,report_doctor_name=null</otherwise></choose>
        ,<choose><when test="null!=operDoctor">oper_doctor_code=#{operDoctor.userName},oper_doctor_name=#{operDoctor.nickName}</when><otherwise>oper_doctor_code=null,oper_doctor_name=null</otherwise></choose>
        <!-- 复审同时保存，不更新审核工作状态 -->
        , result_status_code=case when '3'=result_status_code then result_status_code else '2' end
        <if test="null!=examTime">,exam_time=ifnull(exam_time,#{examTime})</if>
        <if test="null!=reportUrlPdf">,report_url_pdf=#{reportUrlPdf}</if>
        <if test="null!=reportUrlOrgPdf">,report_url_org_pdf=#{reportUrlOrgPdf}</if>
        <if test="null!=reportUploadFilename">,report_upload_filename=#{reportUploadFilename}</if>
        <if test="null!=reportDataSource">,report_data_source=#{reportDataSource}</if>
        <!-- <if test="null!=examDoctor">,exam_doctor_code=ifnull(exam_doctor_code,#{examDoctor.userName}),exam_doctor_name=#{examDoctor.nickName}</if> -->
        <!-- -->
        ,exam_doctor_code=#{examDoctorsCode},exam_doctor_name=#{examDoctorsName}
        ,consultants_code=#{consultantsCode},consultants_name=#{consultantsName}
        ,recorders_code=#{recordersCode},recorders_name=#{recordersName}
        <!-- 工作状态为登记、检查、已报告、审核时允许保存报告 -->
        where (result_status_code is null or result_status_code in ('0','1','2','3')) and id=#{id}
    </update>
	<!-- 删除 -->
    <update id="delete">
        update `d_exam_info` set status=2 where `id`=#{id}
    </update>
    <update id="undoDelete">
        update `d_exam_info` set status=0 where status=2 and `id`=#{id}
    </update>

    <!-- 诊前准备就绪 -->
    <update id="updateExamPrerequire">
        update d_exam_info
        set exam_prerequire=#{examPrerequire}
        where (result_status_code is null or result_status_code in('0','1'))

        <!-- <choose><when test="0==examPrerequire">
        and exam_prerequire is null
        </when><when test="1==examPrerequire">
        and exam_prerequire=0
        </when><otherwise> and 1=0</otherwise></choose> -->

        and  status=0 and id=#{id}
    </update>

    <!-- 下午/上午检查 -->
    <update id="updateExamPeriod">
        update d_exam_info
        set exam_at_pm=#{examAtPm}
        <!-- 只能更新工作状态为已登记和已检查 -->
        where (result_status_code is null or result_status_code in ('0','1')) and  status=0 and id=#{id}
    </update>

    <!-- 审核报告，参数的工作状态（resultStatus）必须时当前数据库中保存的值 -->
    <update id="auditReport">
        update d_exam_info
        <choose>
            <when test='"0"==resultStatus.dictValue or "1"==resultStatus.dictValue'>
                set result_status_code='3',audit_doctor_code=#{auditDoctor.userName},audit_doctor_name=#{auditDoctor.nickName},audit_time=now()
                <if test="null!=examTime">,exam_time=#{examTime}</if>
                <if test="null!=examDoctor">
                    ,exam_doctor_name=#{examDoctor.nickName}
                    ,exam_doctor_code=#{examDoctor.userName}
                </if>
                <if test="null!=examDoctorsName">
                    ,exam_doctor_name=#{examDoctorsName}
                </if>
                <if test="null!=examDoctorsCode">
                    ,exam_doctor_code=#{examDoctorsCode}
                </if>
                where (result_status_code='0' or result_status_code='1'or result_status_code='2')
                and status=0 and id=#{id}
            </when><when test='"2"==resultStatus.dictValue'>
            set result_status_code='3',audit_doctor_code=#{auditDoctor.userName},audit_doctor_name=#{auditDoctor.nickName},audit_time=now()
            where result_status_code='2'
            and  status=0 and id=#{id}
        </when><when test='"3"==resultStatus.dictValue'>
            set result_status_code='4',reaudit_doctor_code=#{reauditDoctor.userName},reaudit_doctor_name=#{reauditDoctor.nickName},reaudit_time=now()
            where result_status_code='3'
            and  status=0 and id=#{id}
        </when><otherwise>where 1=0</otherwise>
        </choose>
    </update>

    <update id="reauditReport">
        update d_exam_info
        set result_status_code='4',reaudit_doctor_code=#{reauditDoctor.userName},reaudit_doctor_name=#{reauditDoctor.nickName},reaudit_time=now()
        ,consultants_name=<choose><when test="null!=consultantsName and ''!=consultantsName">#{consultantsName}</when><otherwise>#{reauditDoctor.nickName}</otherwise></choose>
        where result_status_code='3'
        and  status=0 and id=#{id}
    </update>

    <!-- 签字报告 -->
    <update id="signReport">
        update d_exam_info
        set sign_doctor_code=#{signDoctor.userName},sign_doctor_name=#{signDoctor.nickName}
          ,sign_info_uuid=#{signInfo.uuid}
          ,sign_time=now()
        where result_status_code in ('3','4','11','12')
--         and sign_doctor_code is null
        and status=0 and id=#{id}
    </update>

    <update id="saveDoc">
        update d_exam_info
        set report_url_pdf=#{reportUrlPdf}
        ,report_url_jpg=#{reportUrlJpg}
        ,report_url_password=#{reportUrlPassword}
        ,report_url_username=#{reportUrlUsername}
        where id=#{id}
    </update>

    <update id="sendReportStatus">
        update d_exam_info
        set status_of_send_report=#{statusOfSendReport}
        where id=#{id}
    </update>

<!--    更新未执行医嘱 -->
    <update id="updateUnexecuteOrdId">
        update d_exam_info
        set unexecute_ord_id=#{unexecuteOrdId}
        where id=#{id}
    </update>

    <!-- 获取当前检查项目的最大报告编号 -->
    <select id="getMaxReportNo" resultType="java.lang.String">
        select report_no
        from d_exam_info
        where exam_item_code = #{examItemCode,jdbcType=VARCHAR}
        order by report_no desc limit 1
    </select>
</mapper>