  import {mergeWithNotNull} from "@/utils/common";

  import * as eiapi from "@/assets/scripts/uis/exammanagement/examinfo/api";
  
  import {emptyRegist} from "@/assets/scripts/gis/exammanagement/patient/Regist";

  //工具
  import {cloneDeep, mergeWith as mergeWithDeep} from "lodash";

  //检查状态变更
  import {StatusDict, matchAny as matchAnyStatus} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";
  
  import ExamResultSheet from "@/views/mt/report/comp/ExamResultSheet";

  const model = {
    components: {ExamResultSheet},
    data() {
      return {
        examInfo: emptyRegist()
      }
    },
  
    methods: {
      view(exam) {
        let tpl = emptyRegist();
        eiapi.get(exam.id).then(res => {
          mergeWithDeep(tpl, res.data, null, mergeWithNotNull);
          this.examInfo = tpl;
        })
      },

      save(){
        let postData = cloneDeep(this.examInfo);
        //图像处理
        
        eiapi.saveReport(postData).then(res => {
          this.$modal.msgSuccess("操作完成。");
        }).catch(err => {
          console.error("执行出错, %s", err);
        });
      },
      changeMatch(row) {
        console.log("row:", row);
        this.$refs.examResultSheet.view();
      },
      selectExamResult(row){
        eiapi.matchExamResult(this.examInfo,row).then(res => {
            this.examInfo = res.data;
          });
        console.log("selectExamResult:", row);
      }
    },

    computed: {
      //是否可编辑，报告状态为有效（0），且工作状态为已检查或已报告，一些按钮是否可用
      reportable() {
        const fm = this.examInfo;

        return fm.id //this.editable && 
        && (!fm.status) 
        && matchAnyStatus(fm, StatusDict.regist, StatusDict.exam, StatusDict.report);//(!fm.resultStatus || !fm.resultStatus.dictValue || /^[012]$/.test(fm.resultStatus.dictValue));
      },

      //提交按钮说明
      editButtonLabel() {
        const fm = this.examInfo;
        return matchAnyStatus(fm, StatusDict.regist, StatusDict.exam)? "提交"  : "保存";
      },
    }
  };
  export default model;