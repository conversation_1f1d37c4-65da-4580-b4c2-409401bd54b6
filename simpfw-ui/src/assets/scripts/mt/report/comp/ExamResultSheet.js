//   import { listResult, getResult, delResult, addResult, updateResult } from "@/api/system/result";
import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";
//检查信息接口
import * as eiapi from "@/assets/scripts/uis/exammanagement/examinfo/api";
const model =  {
  name: "Result",
  extends: BaseDialogModel,
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 检查结果表格数据
      resultList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open1: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        reportId: null,
        examItemCode: null,
        deviceDatasourceCode: null,
        uploadTime: null,
        patientName: null,
        patientGenderCode: null,
        patientExamAge: null,
        registNumber: null,
        medicalRecordNumber: null,
        medicalCardNumber: null,
        medicalVisitNumber: null,
        examConclusion: null,
        examDiagnosis: null,
        examSuggestion: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        reportId: [
          { required: true, message: "当前记录的ID不能为空", trigger: "blur" }
        ],
        examItemCode: [
          { required: true, message: "检查项目不能为空", trigger: "blur" }
        ],
        uploadTime: [
          { required: true, message: "上传时间不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    selectRow(row){
        this.triggerBind('selectExamResult', row);
        this.close();
    },
    view() {
        this.open();
        this.getList()
    },
    /** 查询检查结果列表 */
    getList() {
      this.loading = true;
      eiapi.listReportResult(this.queryParams).then(response => {
        this.resultList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open1 = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        reportId: null,
        examItemCode: null,
        deviceDatasourceCode: null,
        uploadTime: null,
        patientName: null,
        patientGenderCode: null,
        patientExamAge: null,
        registNumber: null,
        medicalRecordNumber: null,
        medicalCardNumber: null,
        medicalVisitNumber: null,
        examConclusion: null,
        examDiagnosis: null,
        examSuggestion: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open1 = true;
      this.title = "添加检查结果";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      // getResult(id).then(response => {
      //   this.form = response.data;
      //   this.open = true;
      //   this.title = "修改检查结果";
      // });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
          //   updateResult(this.form).then(response => {
          //     this.$modal.msgSuccess("修改成功");
          //     this.open = false;
          //     this.getList();
          //   });
          } else {
          //   addResult(this.form).then(response => {
          //     this.$modal.msgSuccess("新增成功");
          //     this.open = false;
          //     this.getList();
          //   });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      // this.$modal.confirm('是否确认删除检查结果编号为"' + ids + '"的数据项？').then(function() {
      //   return delResult(ids);
      // }).then(() => {
      //   this.getList();
      //   this.$modal.msgSuccess("删除成功");
      // }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/result/export', {
        ...this.queryParams
      }, `result_${new Date().getTime()}.xlsx`)
    }
  }
};

export default model;