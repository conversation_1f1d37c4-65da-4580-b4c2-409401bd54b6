<template>
  <el-container class="inner-container" style="height: 100%">
    <el-main>
      <ReportWriter ref="reportWriter" @dispatchAction="handleAction"
      @refreshExamInfo="refreshExamInfo"
      @switchTab="switchTab"
      @focusFormField="focusFormField" />
    </el-main>

    <el-aside width="45%">
      <div class="flex-container-column hei100">
        <div class="tabs-wrap flex-item-fill report-tabs-wrap">
          <el-tabs v-model="currentTab" class="tab-ver tab-ver-cp tab-ver-flex hei100" @tab-click="handleTabClick">
            <!-- 本机列表 -->
            <el-tab-pane :label="'待报告列表('+PatientUploadNum + ')'" :name="tabsName.PatientUploadSheet">
              <div class="tab-body-p4 hei100">
                <PatientUploadSheet
                  ref="patientUploadSheet"
                  :refresh="8"
                  :filter="{ status: 0 }"
                  :actions="patientSheetActions"
                  :limitUpload="false"
                  :sheetType="patientSheetType.pendingReport"
                  @dispatchAction="handleAction"
                  @dblClickRow="writeReport"
                  @updatePatientNum="updatePatientNum"
                />
              </div>
            </el-tab-pane>

            <el-tab-pane :label="'待审核列表('+PatientAuditNum + ')'" :name="tabsName.PatientAuditSheet">
              <div class="tab-body-p4 hei100">
                <PatientAuditSheet
                  ref="patientAuditSheet"
                  :refresh="8"
                  :filter="{ status: 0 }"
                  :actions="patientSheetActions"
                  :limitUpload="false"
                  :sheetType="patientSheetType.pendingAudit"
                  @dispatchAction="handleAction"
                  @dblClickRow="writeReport"
                  @updatePatientNum="updatePatientNum"
                />
              </div>
            </el-tab-pane>

            <el-tab-pane label="患者列表" :name="tabsName.PatientSheet">
              <div class="tab-body-p4 hei100">
                <PatientSheet
                  ref="patientSheet"
                  :refresh="8"
                  :filter="{ status: 0 }"
                  :actions="patientSheetActions"
                  :limitUpload="false"
                  :sheetType="patientSheetType.normal"
                  @dispatchAction="handleAction"
                  @dblClickRow="writeReport"
                />
              </div>
            </el-tab-pane>


            <!--  -->
            <!-- <el-tab-pane style="height: 95%;" label="书写模板" :name="tabsName.Template">
              <div class="tab-body-p4 " >
                <TemplateTree  ref="templateTree" @dblclick="applyTemplate" :priflag="true" :targets="portableReportProps" @change="updateReport"/>
              </div>
            </el-tab-pane> -->



            <el-tab-pane label="影像采集" :name="tabsName.ImageMonitor">
              <div class="tab-body-p4 hei100">
                <ImageMonitor ref="imageMonitor" @capture="handleCaptureImage" />
              </div>
            </el-tab-pane>

            <el-tab-pane label="影像提取" :name="tabsName.ImageGallary">
              <div class="tab-body-p4">
                <ImageGallary ref="imageGallary" @select="handleCaptureImage" />
              </div>
            </el-tab-pane>

            <el-tab-pane label="检查结果" :name="tabsName.ExamResult">
              <div class="tab-body-p4">
                <ExamResult ref="examResult" />
              </div>
            </el-tab-pane>
              <!-- <el-tab-pane label="逾期列表" :name="tabsName.OverdueReportList">
              <div class="tab-body-p4">
                <OverdueReportList ref="overdueReportList" :refresh="8"
                  :filter="{ status: 0 }"
                  :actions="patientSheetActions"
                  :limitUpload="false"
                  :sheetType="patientSheetType.normal"
                  @dispatchAction="handleAction"
                  @dblClickRow="writeReport"/>
              </div>
            </el-tab-pane> -->
             <el-tab-pane label="逾期列表" :name="tabsName.OverdueReportList">
              <div class="tab-body-p4 hei100">
                <OverdueReportList
                  :refresh="8"
                  :filter="{ status: 0 }"
                  :actions="patientSheetActions"
                  :limitUpload="false"
                  :sheetType="patientSheetType.normal"
                  @dispatchAction="handleAction"
                  @dblClickRow="writeReport"
                />
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
        <!-- 历史检查 -->
        <ExamHistory ref="examHistory" @row-dblclick="writeReport" @applyReport="applyReport" @view="previewReport" style="height: 220px" />


      </div>
      <!-- 模板预览-->
      <TemplateOverview ref="tplOverview" @applyTemplate="applyTemplate" />
      <!-- 报告预览 -->
      <MTReportViewer ref="reportViewer" mime-type='pdf' />
      <!-- 报告预览 -->
      <!-- <ReportViewer ref="reportViewer" mime-type='jpg' @makeTplDoc="makeTplDoc" /> -->
      <!-- 报告模板 -->
      <ReportTemplate ref="reportTemplate" />
      <!-- 更改房间-->
      <ExamEquipRoom ref="examEquipRoom" :scopable="false" @change="refreshExamInfo" />
      <!-- 呼叫-->
      <ExamCalling ref="examCalling" @refreshExamInfo="refreshExamInfo" />
      <!-- 检查单 -->
      <ExamViewer ref="examViewer" />
      <!-- 危急值 -->
      <CriticalValues ref="criticalValues" />
      <!-- 召回报告 -->
      <ReportAuditWithdraw ref="reportAuditWithdraw" @refreshExamInfo="afterWithdrawReport" />
      <!-- 当前机房 -->
      <!-- <CurrentEquipRoom /> -->

      <!-- 报告预览 -->
      <ReportViewerLoad ref="reportViewerLoad" :viewLoadReport="false"/>

      <!-- 诊室排队叫号界面 add@20230307 -->
      <!-- <QueueNew @callAndWriteReport="writeReport"/> -->
    </el-aside>
  </el-container>
</template>

<style scoped src="@/assets/styles/pacs/report/ReportWriting.css"></style>

<script>
export {default} from "@/assets/scripts/otol/report/MTReportWriting";
</script>
