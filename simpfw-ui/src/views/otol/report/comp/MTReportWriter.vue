<template>
  <div class="report-writer-wrap flex-container-column">
    <div class="report-writer-form flex-item-fill">
      <el-form ref="reportForm" label-width="90px" class="tight-form hei100 flex-container-column">
        <ReportWriterDetail :report="reportForm" v-show="!!reportForm && !!reportForm.id" />
        <!---->


        <div class="report-writer-wrap hei100">
          <!-- 报表查看器 -->
          <ReportView ref="reportView" :initReportPath="reportDSrc"></ReportView>
          <!-- <iframe frameborder="0" :key="ikey" id="reportTable" class="nested-frame-full" :src="reportDSrc"></iframe> -->
        </div>

        <el-row>
          <el-col :span="6">
            <el-form-item label="记录人员">
              <el-input v-model="reportForm.recordersName" size="mini" :disabled="withdrawable"
                class="input-field-narr">
                <el-button slot="append" size="mini" icon="el-icon-user-solid"
                  @click="toPickUser({ multiple: true, target: 'recorders', posts: [{ postCode: 'CSYS' }] })"
                  :disabled="withdrawable"></el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="检查医师">
              <el-input v-model="reportForm.examDoctorsName" size="mini" :disabled="withdrawable"
                class="input-field-narr">
                <el-button slot="append" size="mini" icon="el-icon-user-solid"
                  @click="toPickUser({ multiple: true, target: 'examDoctors', posts: [{ postCode: 'CSYS' }] })"
                  :disabled="withdrawable"></el-button>
                <!-- <i slot="suffix" class="el-input__icon el-icon-search"></i> -->
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="操作者" prop="operDoctor.nickName">
              <el-input v-model="reportForm.operDoctor.nickName" size="mini" :disabled="withdrawable"
                class="input-field-narr">
                <el-button slot="append" size="mini" icon="el-icon-user-solid"
                  @click="toPickUser({ multiple: false, target: 'operDoctor', posts: [{ postCode: 'YS' }], roles: [{ roleKey: 'jdtys' }] })"
                  :disabled="withdrawable"></el-button>
              </el-input>
            </el-form-item>


          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="报告医师">
              <el-input v-model="reportForm.reportDoctor.nickName" size="mini" :disabled="!reportable"
                class="input-field-narr">
                <el-button slot="append" size="mini" icon="el-icon-user-solid"
                  @click="toPickUser({ multiple: false, target: 'reportDoctor' })"
                  :disabled="!reportable"></el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="初审医师">
              <!-- {{reportForm.auditDoctor? reportForm.auditDoctor.nickName : null}} -->
              <img v-if="!!reportForm.signImage" style="height: 25px; vertical-align: middle;"
                :src="'data:image/png;base64,' + reportForm.signImage" />
            </el-form-item>
            <el-form-item label="二审医师" v-if="!!reportForm.secondSignImage">
              <!-- {{reportForm.auditDoctor? reportForm.auditDoctor.nickName : null}} -->
              <img style="height: 25px; vertical-align: middle;"
                   :src="'data:image/png;base64,' + reportForm.secondSignImage" />
            </el-form-item>
            <el-form-item label="三审医师" v-if="!!reportForm.thirdSignImage">
              <!-- {{reportForm.auditDoctor? reportForm.auditDoctor.nickName : null}} -->
              <img style="height: 25px; vertical-align: middle;"
                   :src="'data:image/png;base64,' + reportForm.thirdSignImage" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <div class="buttons-pane-gap buttons-pane-na">

              <!-- <el-button type="primary" @click="editable = true" v-if="!!reportForm.id && !editable">编辑报告</el-button>
            <template v-if="editable"> -->
              <el-button type="danger" size="mini" @click="clearReport" v-loading="saveLoading" v-show="reportable && !transferActive"
             v-hasPermi="['exam-report:write']">报告数据清空</el-button>
             <el-button type="danger" size="mini" @click="resultStatusRollback" v-loading="saveLoading"
             v-show="rollbackable && !transferActive"
             v-hasPermi="['exam-report:write']">状态回退</el-button>

<!--              <el-button type="danger" @click="handleWithdrawAuditReport" v-if="auditRollbackable"
                         v-hasPermi="['exam-report:second_audit', 'exam-report:third_audit']">审核回退</el-button>-->

              <el-button type="primary" size="mini" v-loading="saveLoading" @click="save({ firmed: true })"
                v-show="reportable && !transferActive && writable" v-hasPermi="['exam-report:write']">{{ editButtonLabel }}</el-button>
              <!-- :disabled="currentExamItemCode!='NYST'" -->
              <el-button type="primary" size="mini" @click="handleAudit" v-loading="saveLoading"
                v-show="!transferActive && auditable" v-hasPermi="['exam-report:audit']">报告初审</el-button>
              <el-button type="primary" size="mini" @click="handleAudit" v-loading="saveLoading"
                         v-show="secondAuditable && !transferActive" v-hasPermi="['exam-report:second_audit']">报告二审</el-button>
              <el-button type="primary" size="mini" @click="handleAudit" v-loading="saveLoading"
                         v-show="thirdAuditable && !transferActive" v-hasPermi="['exam-report:third_audit']">报告三审</el-button>
              <!-- <el-button type="primary" @click="handleReaudit"
             v-if="reauditable">复核报告</el-button> -->

              <el-button type="danger" size="mini" @click="handleWithdrawAuditReport" v-show="withdrawable"
                v-hasPermi="['exam-report:audit', 'exam-report:second_audit', 'exam-report:third_audit']">审核召回</el-button>

<!--              <el-button type="danger" @click="handleWithdrawAuditReport" v-if="secondWithdrawable"
                         v-hasPermi="['exam-report:second_audit_withdraw']">审核召回</el-button>

              <el-button type="danger" @click="handleWithdrawAuditReport" v-if="thirdWithdrawable"
                         v-hasPermi="['exam-report:third_audit_withdraw']">审核召回</el-button>-->

              <!-- <el-button type="danger" @click="handleCancel" v-if="reportable">取消</el-button> -->
              <!-- </template> -->
              <el-button type="primary" size="mini" @click="handlePreviewReport" v-loading="saveLoading"
             :disabled="!reportForm.id">预览报告</el-button>
              <el-button type="primary" size="mini" @click="handlePreview('report::print')" v-loading="saveLoading"
                :disabled="!printable" v-hasPermi="['exam-report:print']">打印报告</el-button><!--  v-if="printable" -->
            </div>
          </el-col>
        </el-row>
        

      </el-form>
    </div>

    <div v-if="!splitScreenUsed " class="report-writer-image">

      <div class="exam-image-stat">
        <strong>检查影像</strong>（共有影像<span>{{ null != imagesSet ?
          imagesSet.length:0}}</span>幅，其中<span>{{ numImagesSelected }}</span>幅影像被选中）

        <el-button type="primary" icon="el-icon-paperclip" size="mini" @click="handleResbackImage" v-if="reportable"
          v-hasPermi="['exam-report:write']">导入</el-button>

        <el-button-group style="margin-left: 4px" v-hasPermi="['exam-report:write']">
          <template v-if="!transferActive">
            <el-button type="primary" icon="el-icon-document-add" size="mini" @click="setTransferAction('batch')"
              v-if="reportable">批量转移</el-button>
            <el-button type="primary" icon="el-icon-document-checked" size="mini" @click="setTransferAction('all')"
              v-if="reportable">全部转移</el-button>
          </template><template v-else>
            <el-button type="primary" icon="el-icon-sort" size="mini" @click="batchTransfer"
              v-if="reportable">确定转移</el-button>
            <el-button type="warning" icon="el-icon-d-arrow-left" size="mini" @click="setTransferAction(null)"
              v-if="reportable">取消转移</el-button>
          </template>
        </el-button-group>
      </div>

      <div class="cornerstone-elements-wrap flex-container">
        <div class="cornerstone-elements-scroller cornerstone-elements-scroller-forward" @click="scrollImage(-1)">&lt;
        </div>
        <div ref="imageScrollView" class="cornerstone-elements-pane flex-item-fill"
          :class="{ 'cornerstone-elements-pane-emtpy': !imagesSet || imagesSet.length <= 0 }"
          @mousewheel="scrollImageByWheel">
          <template v-if="null != imagesSet && imagesSet.length > 0" v-for="(item, idx) in imagesSet">
            <div v-if="validateExamImage(item)" :key="item.SOPInstanceUID" :data-index="idx"
              class="cornerstone-element-container" @contextmenu.prevent="showImageContextmenu"><!-- -->
              <!-- 动态影像 -->
              <template v-if="typeofVideo(item)">
                <div class="cornerstone-element cornerstone-element-video" @dblclick="viewVideo($event)" title="双击播放视频">
                  <i class="el-icon-video-camera"></i></div>
              </template>
              <!-- 静态影像 -->
              <template v-else>
                <div class="cornerstone-element" @mouseover="zoomImage($event)" @mouseleave="zoomImage()"
                  @dblclick="viewImage($event)" title="双击看大图"></div>
              </template>
              <!-- 勾选图像序号 -->
              <div class="cornerstone-element-no" v-show="!!item.selectNum && !transferActive">{{ item.selectNum }}</div>
              <!-- 删除按钮 -->
              <div v-if="reportable && !transferActive && typeofJpg(item)" class="cornerstone-element-cls"
                @click="deleteExamImage(item)"><i class="el-icon-delete" /></div>
              <!-- 勾选报告影像 -->
              <div v-if="!transferActive && !isExamImageDeleted(item)" class="exami-mage-checkbox">
                <el-checkbox @change="state => selectExamImage(state, item)" v-model="item.selected"
                  :disabled="!reportable || (!typeofJpg(item) && !typeofDcm(item))" />
              </div>
              <!-- 勾选转移影像 -->
              <div v-if="transferActive && !isExamImageDeleted(item)" class="exami-mage-checkbox">
                <el-checkbox @change="state => selectTransferImage(state, item)" v-model="item.transferSelected"
                  :disabled="!reportable" />
              </div>
              <!-- 删除按钮   -->
              <div v-if="reportable && !transferActive && !isExamImageDeleted(item)" class="cornerstone-element-cls"
                @click="deleteExamImage(item)"><i class="el-icon-delete" /></div>

            </div>
          </template>
        </div>
        <div class="cornerstone-elements-scroller cornerstone-elements-scroller-backward" @click="scrollImage(1)">&gt;
        </div>
        <div class="uploadImage">
          <el-upload :disabled="true" accept="image/*" action="" ref="uploadBtn" multiple :limit="1" class="upload"
            style="width: 100%;">
            <i class="el-icon-plus" ref="uploadImage" @paste="handlePaste" contenteditable="" @keydown="handleFocus"
              v-on:click="pasteClick"><br>将图片粘贴<br>至此处</i>

          </el-upload>

        </div>
      </div>
    </div>
    <!-- 扫码 -->
    <LinksignPopup :qr="qr" />
    <ExamResultDialog ref="examResultDialog" />
    <!-- 查看图像 -->
    <ImageBubble ref="imageBubble" />
    <!-- 播放动态影像 -->
    <VideoView ref="videoView" />
    <!-- 选择医生 -->
    <UserPicker ref="userPicker" @pick="pickUser" />
    <!-- 采集图像右键菜单 -->
    <Contextmenu ref="imageContextmenu" :items="imageContextmenuItems" @select="handleImageContextMenu" />
    <!-- 选择检查信息 -->
    <ExamPicker ref="examPicker" @pick="handleTransferImage" />
    <!-- 上传检查影像 -->
    <ReportImageUploader ref="reportImageUploader" @success="afterResbackImage" />



    <!--  -->
    <el-dialog title="图像浏览" width="800px" :visible.sync="dialogVisible" :modal="false" :append-to-body="false"
      v-dialog-drag>
      <el-row>
        <el-col :span="21">
          <imageView v-if="dialogVisible" :changeUrl="imageURL.uri">
            <img :src="imageURL.uri">
          </imageView>
        </el-col>
        <el-col :span="3" v-if="currentItemIdx != null && !!imagesSet && imagesSet.length > 0">

          <el-button style="margin: 1%;" type="success" v-if="reportable && !imagesSet[currentItemIdx].selected"
            @click="selectExamImage(1, imagesSet[currentItemIdx]); imagesSet[currentItemIdx].selected = !imagesSet[currentItemIdx].selected">选择</el-button>
          <el-button style="margin: 1%;" type="warning" v-if="reportable && imagesSet[currentItemIdx].selected"
            @click="selectExamImage(0, imagesSet[currentItemIdx]); imagesSet[currentItemIdx].selected = !imagesSet[currentItemIdx].selected">取消</el-button>
          <el-button style="margin: 1%;" :disabled="currentItemIdx === 0" type="primary"
            @click="nextView(-1)">上一张</el-button>
          <el-button style="margin: 1%;" :disabled="currentItemIdx === (imagesSet.length - 1)" type="primary"
            @click="nextView(1)">下一张</el-button>
          <el-button class='view-buttom-delete' type="danger" v-if='reportable'
            @click="deleteExamImage(imagesSet[currentItemIdx])">删除</el-button>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<style scoped src="@/assets/styles/pacs/exammanagement/patient/Regist.css"></style>
<style scoped src="@/assets/styles/pacs/report/ReportWriter.css"></style>
<style scoped src="@/assets/styles/pacs/cornerstone-image.css"></style>
<style lang="scss" scoped>
.nested-frame-full {
  width: 100%;
  height: 100%;
  border-width: 0;
}

.el-icon-plus {
  width: 116px;
  height: 116px;
  display: block;
  text-align: center;
  color: blue;
  padding: 40px 0px;
}

.uploadImage {
  width: 120px;
  height: 120px;
  user-select: none;
  border: 1px dashed #d9d9d9;
}
</style>

<script>

export { default } from "@/assets/scripts/otol/report/comp/MTReportWriter";

</script>
