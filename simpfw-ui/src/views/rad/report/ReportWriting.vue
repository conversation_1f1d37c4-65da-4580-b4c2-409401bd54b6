<template>
  <el-container class="inner-container" style="height: 100%">
    <el-main>
      <ReportWriter ref="reportWriter" @dispatchAction="handleAction" 
      @refreshExamInfo="refreshExamInfo"
      @switchTab="switchTab"
      @focusFormField="focusFormField" />
    </el-main>

    <el-aside width="40%">
      <div class="flex-container-column hei100">
        <div class="tabs-wrap flex-item-fill report-tabs-wrap">
          <el-tabs v-model="currentTab" class="tab-ver tab-ver-cp tab-ver-flex hei100" @tab-click="handleTabClick">
            <!-- 本机列表 -->
            <el-tab-pane label="我的工作" :name="tabsName.PatientList" v-if="personalWorkSheetAvail">
              <div class="tab-body-p4 hei100">
                <PersonalWorkSheet ref="patientList" :refresh="8" 
                @dispatchAction="handleAction"
                @dataChange="setPersonalWorkSheetAvail" />
              </div>
            </el-tab-pane>
            <!--  -->
            <el-tab-pane label="患者列表" :name="tabsName.PatientSheet">
              <div class="tab-body-p4 hei100">
                <PatientSheet ref="patientSheet" :filter="patientSheetFilter" :refresh="8"
                  :actions="patientSheetActions"
                  @dispatchAction="handleAction"
                  @dblClickRow="writeReport"
                  @switchTab="switchTab" />
              </div>
            </el-tab-pane>
            <!--  -->
            <el-tab-pane label="申请单信息" :name="tabsName.ApplyExamForm">
              <div class="tab-body-p4">
                <ApplyExamForm ref="examViewer" />
              </div>
            </el-tab-pane>
            <!--  -->
            <el-tab-pane label="报告模板" :name="tabsName.Template">
              <div class="tab-body-p4 hei100 flex-container-column">
                <TemplateTree ref="templateTree" :htooltip="false" :priflag="true"
                 @click="showTemplate" 
                 @dblclick="applyTemplate" 
                 style="height: 50%; overflow: auto;"  />
                <div class="buttons-pane-gap" style="padding-top: 2px; border-top: 1px solid #DDD;">
                  <el-button type="primary" @click="addTemplateCate">添加目录</el-button>
                  <el-button type="primary" @click="addTemplate">添加模板</el-button>
                  <el-button type="primary" @click="editTemplate" :disabled="!reportTemplate || !reportTemplate.data || !reportTemplate.data.id">修改</el-button>
                  <el-button type="primary" @click="deleteTemplate" :disabled="!reportTemplate || !reportTemplate.data || !reportTemplate.data.id">删除</el-button>
                </div>
                <div>
                  <el-input v-model="reportTemplate.data.templateName" placehoder="模板名称"
                   :disabled="!reportTemplateEditable"></el-input>
                </div>
                <el-card class="nested-card card-pane flex-container-column rw-he-card" style="height: 25%">
                  <div slot="header">
                    <span class="nested-card-header-title">所见</span>
                  </div>
                  <div class="hei100 pre-pane">
                    <el-input type="textarea" :rows="4" class="hei100" v-model="reportTemplate.data.examDesc"
                     :disabled="!reportTemplateEditable"></el-input>
                  </div>
                </el-card>
                <el-card class="nested-card card-pane flex-item-fill flex-container-column rw-he-card">
                  <div slot="header">
                    <span class="nested-card-header-title">诊断</span>
                  </div>
                  <div class="hei100 pre-pane">
                    <el-input type="textarea" :rows="2" class="hei100" v-model="reportTemplate.data.examDiagnosis"
                     :disabled="!reportTemplateEditable"></el-input>
                  </div>
                </el-card>
                <div class="buttons-pane buttons-pane-gap">
                  <el-button type="primary" @click="saveReportAsTemplate">报告保存为模板</el-button>
                  <span>
                    <el-button type="primary" @click="saveTemplate" :disabled="!reportTemplateEditable">保存</el-button>
                    <el-button type="primary" @click="showTemplate()">取消</el-button>
                  </span>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="常用符号及短语">
              <el-container style="height: 100%;">
                <el-aside style="width: 40% ;border-right : 1px solid #cfcfcf;">
                  <div style="margin-top: 8px;">
                    <span>常用符号</span>
                    <SymbolSelect :targets="portableReportProps" @change="updateReport" />
                  </div>
                </el-aside>
                <el-main style="margin-left: 10px;">
                  <div style="margin-top: 8px;">
                    <span>常用短语</span>
                    <PhraseSelect :targets="portableReportProps" @change="updateReport" />
                  </div>
                </el-main>
              </el-container>
            </el-tab-pane>
            <!--  -->
            <el-tab-pane label="历史检查" :name="tabsName.ExamHistory">
              <div class="tab-body-p4 hei100 flex-container-column">
                <ExamHistory ref="examHistory" :showLabel="false" style="height: 50%"
                 @select="handleHistExamSelected" @row-dblclick="handleHistExamDblClick" />
                <el-card class="nested-card card-pane flex-container-column rw-he-card" style="height: 25%">
                  <div slot="header">
                    <span class="nested-card-header-title">检查所见</span>
                  </div>
                  <div class="flex-item-fill pre-pane">{{!!histExamItem? histExamItem.examDesc : null}}</div>
                </el-card>
                <el-card class="nested-card card-pane flex-container-column rw-he-card" style="height: 25%">
                  <div slot="header">
                    <span class="nested-card-header-title">检查诊断</span>
                  </div>
                  <div class="flex-item-fill pre-pane">{{!!histExamItem? histExamItem.examDiagnosis : null}}</div>
                </el-card>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
        
      </div>
      <!-- 报告预览 -->
      <ReportPreviewer ref="reportPreviewer" />
      <!-- 危急值 -->
      <CriticalValues ref="criticalValues" />
      <!-- 召回报告 -->
      <ReportAuditWithdraw ref="reportAuditWithdraw" @refreshExamInfo="afterWithdrawReport" />
      <!-- 当前机房
      <CurrentEquipRoom /> -->
      <!-- 模板分类 -->
      <TplCateEditForm ref="tplCateEditForm" @refresh="afterAddTemplateCate" />
      <!-- 模板管理 -->
      <TemplateIndex ref="templateIndex" @refresh="afterAddTemplate" />
    </el-aside>
  </el-container>
</template>

<style scoped src="@/assets/styles/pacs/report/ReportWriting.css"></style>
<style scoped>
.rw-he-card >>> .el-card__body{
  overflow: auto;
}
.pre-pane{
  white-space: pre-wrap;
  overflow: auto;
}
.hei100.el-textarea >>> .el-textarea__inner{
  height: 100%;
}
</style>

<script>
import model from "@/assets/scripts/rad/report/ReportWriting";
export default model;
</script>
