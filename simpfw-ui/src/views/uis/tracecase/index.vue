<template>
	<el-container style="height: 1000px; border: 1px solid #eee; ">
	  <el-aside width="200px" style="background-color: rgb(238, 241, 246)">
	    <el-menu default-active="2">
	      <el-submenu index="1">
	        	<template slot="title"><i class="el-icon-menu"></i>病例追踪列表({{countsMap.totalCount || 0}})</template>
	          	<el-menu-item index="2"  @click="queryByStatus(2)">正在追踪({{countsMap["2"] || 0}})</el-menu-item>
	          	<el-menu-item index="3"  @click="queryByStatus(3)">继续追踪({{countsMap["3"] || 0}})</el-menu-item>
	          	<el-menu-item index="4"  @click="queryByStatus(4)">追踪完成({{countsMap["4"] || 0}})</el-menu-item>
	      </el-submenu>
	    </el-menu>
	  </el-aside>
	  
	  <el-container>
	    <el-main>

        <!-- 查询表单 -->
        <el-form ref="traceCaseQueryForm" :model="queryParams" label-width="80px">
          <el-form-item label="追踪日期" style="margin-bottom: 2px;">
            <el-col :span="5">
              <el-date-picker size="small" value-format="yyyy-MM-dd" type="date" placeholder="选择日期" v-model="queryParams.startTraceDate" style="width: 100%;"></el-date-picker>
            </el-col>
            <el-col class="line" :span="1" style="margin-left: 26px;">至</el-col>
            <el-col :span="5">
              <el-date-picker  size="small" value-format="yyyy-MM-dd" type="date" placeholder="选择日期" v-model="queryParams.endTraceDate" style="width: 100%;"></el-date-picker>
            </el-col>

            <el-col :span="5">
              <el-form-item label="追踪医生">
                <input type="hidden" name="traceDoctorCode" v-model="queryParams.traceDoctorCode"/>
                <el-input v-model="queryParams.traceDoctorName" placeholder="请选择医生" clearable class="el-input-group-thin" @clear="clearTraceDoctor" style="width: 150px;">
                    <el-button el-button slot="append" size="mini" icon="el-icon-user-solid" 
                     title="选择医师"
                     @click="toPickUser('reqDoctor',  [{postCode:'YS'}])"></el-button>
                </el-input>

              </el-form-item>
            </el-col>
          </el-form-item>

          <el-form-item label="检查日期">
            <el-col :span="5">
              <el-date-picker  size="small" value-format="yyyy-MM-dd" type="date" placeholder="选择日期" v-model="queryParams.startExamDate" style="width: 100%;"></el-date-picker>
            </el-col>
            <el-col class="line" :span="1" style="margin-left: 26px;">至</el-col>
            <el-col :span="5">
              <el-date-picker  size="small" value-format="yyyy-MM-dd" type="date" placeholder="选择日期" v-model="queryParams.endExamDate" style="width: 100%;"></el-date-picker>
            </el-col>
          </el-form-item>

          <el-col :span="10">
            <el-form-item label="检查号" style="margin-left: 0px;">
              <el-input  size="small" v-model="queryParams.examNo" style="width: 160px;" placeholder="请输入检查号" clearable></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="5">
            <el-form-item label="姓名" style="margin-left: -135px;">
              <el-input size="small" v-model="queryParams.patientName" style="width: 160px;" placeholder="请输入姓名" clearable></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="2">
            <el-form-item style="margin-left: 10px;">
              <el-button type="primary" @click="search">查询</el-button>
              <!-- <el-button type="primary" @click="handleAdd">新建</el-button> -->
            </el-form-item>
          </el-col>
        </el-form>


        <!-- 数据列表 -->
	      <el-table :data="tableData" height="350" style="margin-top: 10px; white-space: nowrap;" border >
          <el-table-column prop="traceStatus" label="状态" width="100" :formatter="formatterTraceStatus"></el-table-column>
	        <el-table-column prop="examInfo.examNo" label="检查号" width="120"></el-table-column>
          <el-table-column prop="patientInfo.name" label="姓名" width="120"></el-table-column>
	        <!-- <el-table-column prop="address" label="类型"></el-table-column> -->
          <el-table-column prop="patientInfo.genderCode" label="性别" :formatter="formatterGender"></el-table-column>
          <el-table-column prop="patientInfo.birthday" label="年龄" :formatter="formatterAge"></el-table-column>
          <el-table-column prop="planTraceDate" label="计划追踪日期" width="100px"></el-table-column>
          <el-table-column prop="traceDate" label="追踪日期" width="100px"></el-table-column>
          <el-table-column prop="traceDoctorName" label="追踪医生"></el-table-column>
          <el-table-column prop="confirmStatus" label="符合情况" :formatter="formatterConfirmStatus"></el-table-column>
          
          <el-table-column prop="examConclusion" label="追踪原因"  width="320">
            <template slot-scope="scope">
              {{scope.row.examConclusion | textCut(120)}}
            </template>
          </el-table-column>

          <el-table-column prop="createTime" label="创建时间" width="160px"></el-table-column>
          <el-table-column prop="updateTime" label="更新时间" width="160px"></el-table-column>
          <el-table-column label="操作" fixed="right" width="160px">
            <template slot-scope="scope">
              <el-button @click="handleAddOrEditClick(scope.row)" type="text" size="large">详情</el-button>
            </template>
          </el-table-column>
	      </el-table>

        <div class="block pagination-container">
          <el-pagination
            v-show="totalCount > 0"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryParams.pageNum"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="queryParams.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalCount">
          </el-pagination>
        </div>



        <!-- 新增或修改 -->
        <el-dialog
          title="病例追踪-详情"
          :visible.sync="isEditTraceCase"
          v-if="isEditTraceCase"
          width="80%"
          :show-close="false"
          close-on-click-modal
          @open="getTraceCaseDataWhenOpen"
          :close="handleClose">
            <div style="height: 620px;">
              <AddEditTraceCaseView :examNoFromParentComponent="traceCaseExamNo"  @handleClose="handleClose" @refresh="refresh"/>
            </div>
        </el-dialog>

	    </el-main>

      <!-- 选择医生 -->
      <UserPicker ref="userPicker" @pick="pickUser" />

	  </el-container>
	</el-container>
</template>



<style scoped>
  .el-header {
    background-color: #B3C0D1;
    color: #333;
    line-height: 60px;
  }
  
  .el-aside {
    color: #333;
  }

  .el-form-item {
    margin-bottom: 10px;
  }

</style>



<script>
  import Request from "@/utils/request.js";
  import AddTraceCaseView from "./add";
  import AddEditTraceCaseView from "./add_edit";
  import UserPicker from "@/views/system/user/comp/UserPicker";

  export default {
      name: 'TraceCase',
      components: {
          AddTraceCaseView,
          AddEditTraceCaseView,
          UserPicker,
      },
      
     data() {
      return {

          totalCount: 0,
          tableData: [],

          // 查询参数
          queryParams: {
            pageNum: 1,
            pageSize: 10,
            startTraceDate: '',
            endTraceDate: '',
            startExamDate: '',
            endExamDate: '',
            traceDoctorCode: '',
            traceDoctorName: '',
            examNo: '',
            patientName: '',
            traceStatus: '2'
          },
          countsMap: {},

          isAddTraceCase: false,
          isEditTraceCase: false,
          traceCaseExamNo: '',
      }
    },

    mounted() {
      console.log("mounted-------------");
      this.getCountsByTraceStatus();
      this.getDataList();
    },

    filters: {
      textCut: function (value, reserveLen) {
        if (!value) return '';

        if (!reserveLen) {
          reserveLen = 100;
        }
        value = value.toString()
        return value.substring(0, reserveLen) + "...";
      }
    }, 

    methods: {
      search() {
        this.getDataList();
      },
      refresh() {
        this.getCountsByTraceStatus();
        this.getDataList();
      },
      queryByStatus(traceStatus) {
        console.log(traceStatus);
        this.queryParams.traceStatus = traceStatus;
        this.getDataList();
      },

      getDataList() {
        Request.post("/exammanagement/traceCase/list", this.queryParams)
          .then(res => {
            console.log(res);
            this.totalCount = res.total;
            this.tableData = res.rows;
          })
          .catch(function (error) {
              console.log(error);
          });
      },

      getCountsByTraceStatus() {
        Request.get("/exammanagement/traceCase/getCounts")
          .then(res => {
            // console.log(res);
            this.countsMap = res.data;
          })
          .catch(function (error) {
              console.log(error);
          });
      },

      handleSizeChange(pageSize) {
        this.queryParams.pageSize = pageSize;
        this.getDataList();
      },
      handleCurrentChange(pageNum) {
        this.queryParams.pageNum = pageNum;
        this.getDataList();
      },

      handleAdd() {
        this.isAddTraceCase = true;
      },

      handleClose() {
        this.isEditTraceCase = false;
      },

      formatterTraceStatus(row, column, cellValue, index) {
          switch(cellValue) {
            case 0:
              return "不追踪";
            case 1:
              return "计划追踪";
            case 2:
              return "正在追踪";
            case 3:
              return "继续追踪";
            case 4:
              return "完成追踪";
          }
      },
      formatterGender(row, column, cellValue, index) {
          switch(cellValue) {
            case "M":
              return "男";
            case "F":
              return "女";
            default:
              return "未知";
          }
      },
       formatterAge(row, column, cellValue, index) {
          if (!cellValue) {
            return "未知";
          }

          var birthday = new Date(cellValue);
          var curr = new Date();
          var diff = (curr.getTime() - birthday.getTime())/(1000 * 60 * 60 * 24);  // 天
          if (diff >= 365) {
              return Math.floor(diff/365) + "岁";
          } else if (diff > 30 && diff < 365) {
              return Math.floor(diff/30) + "个月";
          } else {
              return Math.floor(diff/30) + "天";
          }
      },
      formatterConfirmStatus(row, column, cellValue, index) {
        switch(cellValue) {
            case 0:
              return "符合";
            case 1:
              return "基本符合";
            case 2:
              return "不符合";
            default:
              return "未知";
          }
      },
      clearTraceDoctor() {
          this.queryParams.traceDoctorCode = '';
          this.queryParams.traceDoctorName = '';
      },

      handleAddOrEditClick(rowData) {
        if (rowData) {
          this.traceCaseExamNo = rowData.examInfo.examNo;
        }
        this.isEditTraceCase = true;
      },
      getTraceCaseDataWhenOpen() {
          console.log("this.traceCaseExamNo: ", this.traceCaseExamNo);
      },



      // --- 选择医生，copy from Regist.js
      toPickUser(tar, posts) {
        this.$refs["userPicker"].showPicker({target: tar, posts});
      },
      pickUser(tar, usr) {
        const fm = this.$refs.traceCaseQueryForm;

          // console.log("tar: ", tar);
          // console.log("usr: ", usr);
        if (tar) {
          if('examDoctors' === tar) {
            fm[`${tar}Code`] = usr.userName;
            fm[`${tar}Name`] = usr.nickName;
          } else {
            fm[tar] = usr;
          }
          this.queryParams.traceDoctorCode = usr.userName;
          this.queryParams.traceDoctorName = usr.nickName;
        }
      },



    }

  };

</script>