<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--数据-->
      <el-col :span="24" :xs="24">
        <el-form :model="searchForm" ref="searchForm" size="small" :inline="true" class="tight-form" v-show="showSearch">
          <el-form-item label="患者姓名">
            <el-input v-model="searchForm.patientInfo.name" clearable />
          </el-form-item>
          <el-form-item label="检查流水">
            <el-input v-model="searchForm.id" clearable />
          </el-form-item>
          <el-form-item label="病历号">
            <el-input v-model="searchForm.ordId" clearable />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="grid.data" row-key="id"
         stripe highlight-current-row>
          <el-table-column prop="id" label="检查流水号" width="100" />
          <el-table-column prop="examNo" label="排队号" width="160" />
          <el-table-column prop="patientInfo.name" label="姓名" width="160" />
          <el-table-column prop="examItem.dictLabel" label="检查项目" :formatter="colFmt_object" />
          <el-table-column prop="examParts.partsName" label="检查部位" width="200" :formatter="colFmt_object" />
          <el-table-column label="操作" width="100" align="center" class-name="button-col">
            <template slot-scope="scope">
              <el-button title="编辑检查信息"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
              ></el-button>
              <el-button :title="buttonTipState"
                :icon="buttonIconState?buttonIconState:'el-icon-check'"
                @click="handleChange(scope.row)"
              ></el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="grid.total>0"
          :total="grid.total"
          :page.sync="searchForm.pageNum"
          :limit.sync="searchForm.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>
  </div>
</template>

<style scoped>
.exam-ready i{
  color: green;
}
</style>

<script>
import model from "@/assets/scripts/gis/exammanagement/patient/comp/Examnn";
export default model;
</script>
