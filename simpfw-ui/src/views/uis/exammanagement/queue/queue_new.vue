<template>
    <div>
        <div class="float-drag-button" @click="openQueueView">
            <span class="queue-span">排队叫号</span>
        </div>

        <el-drawer
            :visible.sync="showQueueView"
            :with-header="false"
            size="80%">

            <div v-if="hasSelectEquitRoom" style="height: 30px; margin-top: 10px; margin-bottom: 10px; text-align: center; font-size: 18px;">
                <span>房间名称：{{equipRoomObj.roomName }} </span>
                &nbsp;&nbsp;&nbsp;&nbsp;
                <span style="margin-left: 60px; font-size: 14px;">检查项目：{{equipRoomObj.examItemName}}</span>
            </div>

            <el-row :gutter="10">
                <el-col :span="20">
                    <el-tabs type="border-card">
                        <el-tab-pane label="全部排队">
                            <div style="margin-left: 10px; margin-right: 2px; height: 500px; overflow: scroll;">
                                <h1 style="color: #6495ED; font-size: 16px; font-weight: bold; background-color: #D4F4C5;">大厅排队列表（{{ queueingList.length }}）</h1>
                                <el-table :data="queueingList" height="400" style="margin-top: -20px; white-space: nowrap;" >
                                    <el-table-column type="index" label="序号" width="50"></el-table-column>
                                    <!-- <el-table-column prop="queueStatus" label="状态" width="70"></el-table-column> -->
                                    <el-table-column prop="callNo" label="排队号" width="70"></el-table-column>
                                    <el-table-column prop="patientName" label="姓名" width="100"></el-table-column>
                                    <el-table-column prop="gender" label="性别" width="50"></el-table-column>
                                    <el-table-column prop="birthday" label="年龄" width="60" :formatter="formatterAge"></el-table-column>
                                    <el-table-column prop="exampartName" label="检查部位" width="180">
                                        <template slot-scope="scope">
                                            <el-tooltip class="item" effect="dark" :content="scope.row.exampartName" placement="top-start">
                                                <span>{{scope.row.exampartName | textCut(15)}}</span>
                                            </el-tooltip>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="examItemName" label="检查项目" width="105px"></el-table-column>
                                    <el-table-column prop="inpTypeName" label="就诊类型" width="80px"></el-table-column>
                                    <el-table-column prop="examNo" label="检查号" width="120"></el-table-column>
                                    <el-table-column prop="regNo" label="登记号" width="90"></el-table-column>
                                    <el-table-column prop="registerTime" label="登记时间" width="160px"></el-table-column>
                                    <el-table-column label="操作" fixed="right" width="120px">
                                        <template slot-scope="scope">
                                            <el-button type="success" size="mini" icon="el-icon-phone-outline" title="叫号" style="margin-right: 5px;" @click="selectCall(scope.row)"></el-button>
                                            <!-- <el-button type="danger" size="mini" icon="el-icon-delete" title="恢复排队" @click="resetCall(scope.row)"></el-button> -->
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </el-tab-pane>
                        
                        <el-tab-pane label="本诊室检查">
                            <div style="margin-left: 10px; margin-right: 10px;">
                                <h1 style="color: #6495ED; font-size: 16px; font-weight: bold; background-color: #D4F4C5;">正在就诊（{{ examingList.length }}）</h1>
                                <el-table :data="examingList" height="90" style="margin-top: -20px; white-space: nowrap;" >
                                    <!-- <el-table-column prop="queueStatus" label="状态" width="70"></el-table-column> -->
                                    <el-table-column prop="callNo" label="排队号" width="70"></el-table-column>
                                    <el-table-column prop="patientName" label="姓名" width="100"></el-table-column>
                                    <el-table-column prop="gender" label="性别" width="50"></el-table-column>
                                    <el-table-column prop="birthday" label="年龄" width="60" :formatter="formatterAge"></el-table-column>
                                    <el-table-column prop="exampartName" label="检查部位" width="180">
                                        <template slot-scope="scope">
                                            <el-tooltip class="item" effect="dark" :content="scope.row.exampartName" placement="top-start">
                                                <span>{{scope.row.exampartName | textCut(15)}}</span>
                                            </el-tooltip>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="examItemName" label="检查项目" width="105px"></el-table-column>
                                    <el-table-column prop="inpTypeName" label="就诊类型" width="80px"></el-table-column>
                                    <el-table-column prop="examNo" label="检查号" width="120"></el-table-column>
                                    <el-table-column prop="regNo" label="登记号" width="90"></el-table-column>
                                    <el-table-column prop="registerTime" label="登记时间" width="160px"></el-table-column>
                                    <el-table-column label="操作" fixed="right" width="120px">
                                        <template slot-scope="scope">
                                            <!-- <el-button type="success" size="mini" icon="el-icon-phone-outline" title="叫号" style="margin-right: 5px;" @click="selectCall(scope.row)"></el-button> -->
                                            <el-button type="danger" size="mini" icon="el-icon-delete" title="恢复排队" @click="resetCall(scope.row)"></el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                            
                            <div style="margin-bottom: 20px;"></div>

                            <div style="margin-left: 10px; margin-right: 10px;">
                                <h1 style="color: #6495ED; font-size: 16px; font-weight: bold; background-color: #D4F4C5;">候诊患者（{{ waitingList.length }}）</h1>
                                <el-table :data="waitingList" height="200" style="margin-top: -20px; white-space: nowrap;" >
                                    <!-- <el-table-column prop="queueStatus" label="状态" width="70"></el-table-column> -->
                                    <el-table-column type="index" label="序号" width="50"></el-table-column>
                                    <el-table-column prop="callNo" label="排队号" width="70"></el-table-column>
                                    <el-table-column prop="patientName" label="姓名" width="100"></el-table-column>
                                    <el-table-column prop="gender" label="性别" width="50"></el-table-column>
                                    <el-table-column prop="birthday" label="年龄" width="60" :formatter="formatterAge"></el-table-column>
                                    <el-table-column prop="exampartName" label="检查部位" width="180">
                                        <template slot-scope="scope">
                                            <el-tooltip class="item" effect="dark" :content="scope.row.exampartName" placement="top-start">
                                                <span>{{scope.row.exampartName | textCut(15)}}</span>
                                            </el-tooltip>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="examItemName" label="检查项目" width="105px"></el-table-column>
                                    <el-table-column prop="inpTypeName" label="就诊类型" width="80px"></el-table-column>
                                    <el-table-column prop="examNo" label="检查号" width="120"></el-table-column>
                                    <el-table-column prop="regNo" label="登记号" width="90"></el-table-column>
                                    <el-table-column prop="registerTime" label="登记时间" width="160px"></el-table-column>
                                    <el-table-column label="操作" fixed="right" width="120px">
                                        <template slot-scope="scope">
                                            <el-button type="success" size="mini" icon="el-icon-phone-outline" title="叫号" style="margin-right: 5px;" @click="selectCall(scope.row)"></el-button>
                                            <el-button type="danger" size="mini" icon="el-icon-delete" title="恢复排队" @click="resetCall(scope.row)"></el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                            
                            <div style="margin-bottom: 20px;"></div>

                            <div style="margin-left: 10px; margin-right: 10px;">
                                <h1 style="color: #6495ED; font-size: 16px; font-weight: bold; background-color: #D4F4C5;">过号患者（{{ missedList.length }}）</h1>
                                <el-table :data="missedList" height="200" style="margin-top: -20px; white-space: nowrap;" >
                                    <!-- <el-table-column prop="queueStatus" label="状态" width="70"></el-table-column> -->
                                    <el-table-column type="index" label="序号" width="50"></el-table-column>
                                    <el-table-column prop="callNo" label="排队号" width="70"></el-table-column>
                                    <el-table-column prop="patientName" label="姓名" width="100"></el-table-column>
                                    <el-table-column prop="gender" label="性别" width="50"></el-table-column>
                                    <el-table-column prop="birthday" label="年龄" width="60" :formatter="formatterAge"></el-table-column>
                                    <el-table-column prop="exampartName" label="检查部位" width="180">
                                        <template slot-scope="scope">
                                            <el-tooltip class="item" effect="dark" :content="scope.row.exampartName" placement="top-start">
                                                <span>{{scope.row.exampartName | textCut(15)}}</span>
                                            </el-tooltip>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="examItemName" label="检查项目" width="105px"></el-table-column>
                                    <el-table-column prop="inpTypeName" label="就诊类型" width="80px"></el-table-column>
                                    <el-table-column prop="examNo" label="检查号" width="120"></el-table-column>
                                    <el-table-column prop="regNo" label="登记号" width="90"></el-table-column>
                                    <el-table-column prop="registerTime" label="登记时间" width="160px"></el-table-column>
                                    <el-table-column label="操作" fixed="right" width="120px">
                                        <template slot-scope="scope">
                                            <el-button type="success" size="mini" icon="el-icon-phone-outline" title="叫号" style="margin-right: 5px;" @click="selectCall(scope.row)"></el-button>
                                            <el-button type="danger" size="mini" icon="el-icon-delete" title="恢复排队" @click="resetCall(scope.row)"></el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </el-tab-pane>
                    </el-tabs>
                </el-col>
                
                <el-col :span="4">
                    <div style="margin-top: 10px; margin-left: -30px; text-align: center;">
                        <el-form label-width="-10px">
                            <el-form-item>
                                <el-button type="primary" style="margin-left: 5px; width:50%;" size="" @click="callFromWaitingroom">大厅呼叫</el-button>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" style="margin-left: 5px; width:50%;" size="" @click="nextCall">诊室呼叫<br/>(下一个)</el-button>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" style="margin-left: 5px; width:50%;" size="" @click="recall">复呼</el-button>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" style="margin-left: 5px; width:50%;" size="" @click="missedCall">过号</el-button>
                            </el-form-item>
                        </el-form>
                        <!-- 

                            <el-button type="primary" style="margin-left: 5px;" size="small" @click="callFromWaitingroom">大厅呼叫</el-button>
                            <el-button type="primary" style="margin-left: 5px;" size="small" @click="nextCall">诊室呼叫(下一个)</el-button>
                            <el-button type="primary" style="margin-left: 5px;" size="small" @click="recall">复呼</el-button>
                            <el-button type="primary" style="margin-left: 5px;" size="small" @click="missedCall">过号</el-button>
                        -->
                    </div>
                </el-col>
            </el-row>

        </el-drawer>
    </div>
</template>

<style>
    .float-drag-button {
        position: absolute;
        right: 0;
        top: 10%;
        z-index: 20000;
        padding: 5px;
        width: 25px;
        opacity: 1;
        background-color: #26d40e;
        border-radius: 8px 0px 0px 8px;
        box-shadow: 0px 2px 15px 0px rgba(9,41,77,0.15);
        cursor: pointer;
        font-size: 14px;
        color: #FFFFFF;
    }
</style>


<script scope>
    import Request from "@/utils/request.js";
    import {setRoom, getRoom, clearRoom} from "@/utils/equip-room"

    export default {
      name: 'QueueNew',
      components: {
      },

      data() {
        return {
            showQueueView: false,
            hasSelectEquitRoom: false,
            currEquitRoomName: '',
            activeNames: ['1'],
            loading: false,
            // 排队号队列
            queueingList:[],
            waitingList:[],
            examingList:[],
            missedList:[],
            equipRoomObj: {},
        }
      },
      props: {
        // nav1: true
      },
      mounted() {
        this.getQueueingList();
      },
      filters: {
        textCut: function (value, reserveLen) {
            if (!value) return '';

            if (!reserveLen) {
            reserveLen = 100;
            }
            value = value.toString()
            return value.substring(0, reserveLen) + "...";
        }
      }, 
      methods: {
        openQueueView() {
            let currEquitRoom = getRoom();
            if (!currEquitRoom || currEquitRoom.roomCode == "") {
                this.showQueueView = false;
                return;
            }
            this.showQueueView = !this.showQueueView;
            console.log("-this.showQueueView: ", this.showQueueView);
            if (this.showQueueView) {
                this.refresh();
            }
        },

        refresh() {
            this.getQueueingList();
        },
        // 排队列表
        getQueueingList() {
            let currEquitRoom = getRoom();
            if (!currEquitRoom || currEquitRoom.roomCode == "") {
               return;
            }
            this.currEquitRoomName = currEquitRoom.roomName;
            this.hasSelectEquitRoom = true;
            let currEquipRoomCode = currEquitRoom.roomCode;
            Request.post("/exammanagement/queueV2/getExamItemQueuesByRoomCode", {
                "equipRoomCode": currEquipRoomCode,
            })
            .then(res => {
                console.log("getExamItemQueuesByRoomCode: ", res);
                let retMap = res.data;
                if (retMap) {
                    this.queueingList = retMap["queueingList"] || [];
                    this.waitingList = retMap["waitingList"] || [];
                    this.examingList = retMap["examingList"] || [];
                    this.missedList = retMap["missedList"] || [];
                    this.equipRoomObj = retMap["equipRoomObj"] || {};
                }
            })
            .catch(function (error) {
                console.log(error);
            });
        },

        // 大厅呼叫
        callFromWaitingroom() {
            let currEquitRoom = getRoom();
            if (!currEquitRoom) {
               return;
            }
            Request.post("/exammanagement/queueV2/callFromWaitingroom", {
                "equipRoomCode": currEquitRoom.roomCode,
            })
            .then(res => {
                console.log("callFromWaitingroom: ", res);
                let retMap = res.data;
                if (retMap) {
                    let msg = "大厅呼叫 " + retMap.callNo + "号 " + retMap.patientName + " 候诊成功";
                    this.$modal.msgSuccess(msg);
                    this.refresh();
                }
            })
            .catch(function (error) {
                console.log(error);
            });
        },

        // 复呼
        recall() {
            let currEquitRoom = getRoom();
            if (!currEquitRoom) {
               return;
            }
            Request.post("/exammanagement/queueV2/recall", {
                "equipRoomCode": currEquitRoom.roomCode,
            })
            .then(res => {
                let retMap = res.data;
                if (retMap) {
                    let msg = "复呼 " + retMap.callNo + "号 " + retMap.patientName + " 成功";
                    this.$modal.msgSuccess(msg);
                    // this.refresh();
                }
            })
            .catch(function (error) {
                console.log(error);
            });
        },

        // 过号
        missedCall() {
            let currEquitRoom = getRoom();
            if (!currEquitRoom) {
               return;
            }
            Request.post("/exammanagement/queueV2/missedCall", {
                "equipRoomCode": currEquitRoom.roomCode,
            })
            .then(res => {
                let retMap = res.data;
                if (retMap) {
                    let msg = retMap.callNo + "号 " + retMap.patientName + " 已过号";
                    this.$modal.msgSuccess(msg);
                    this.refresh();

                    // const obj = { path: "/ReportWriting", name: "ReportWriting" };
                    // this.$tab.refreshPage(obj);
                }
            })
            .catch(function (error) {
                console.log(error);
            });
        },

        // 下一个/诊室呼叫
        nextCall() {
            let currEquitRoom = getRoom();
            if (!currEquitRoom) {
               return;
            }
            Request.post("/exammanagement/queueV2/nextCall", {
                "equipRoomCode": currEquitRoom.roomCode,
            })
            .then(res => {
                let retMap = res.data;
                if (retMap) {
                    let msg = "呼叫 " + retMap.callNo + "号 " + retMap.patientName + " 成功";
                    this.$modal.msgSuccess(msg);
                    this.refresh();

                    this.$emit('callAndWriteReport', retMap.examInfo);
                    this.showQueueView = false;
                }
            })
            .catch(function (error) {
                console.log(error);
            });
        },

        // 选呼
        selectCall(row) {
            
            let currEquitRoom = getRoom();
            if (!currEquitRoom) {
               return;
            }
            Request.post("/exammanagement/queueV2/selectCall", {
                "equipRoomCode": currEquitRoom.roomCode,
                "callNo": row.callNo,
            })
            .then(res => {
                let retMap = res.data;
                if (retMap) {
                    let msg = "呼叫 " + retMap.callNo + "号 " + retMap.patientName + " 成功";
                    this.$modal.msgSuccess(msg);
                    this.refresh();

                    this.$emit('callAndWriteReport', retMap.examInfo);
                    this.showQueueView = false;
                }
            })
            .catch(function (error) {
                console.log(error);
            });
        },

        // 恢复排队
        resetCall(row) {
            let currEquitRoom = getRoom();
            if (!currEquitRoom) {
               return;
            }
            Request.post("/exammanagement/queueV2/resetCall", {
                "equipRoomCode": currEquitRoom.roomCode,
                "callNo": row.callNo,
            })
            .then(res => {
                let retMap = res.data;
                if (retMap) {
                    let msg = retMap.callNo + "号 " + retMap.patientName + " 恢复排队成功";
                    this.$modal.msgSuccess(msg);
                    this.refresh();
                }
            })
            .catch(function (error) {
                console.log(error);
            });
        },

    
        // ---
        formatterAge(row, column, cellValue, index) {
          if (!cellValue) {
            return "未知";
          }

          var birthday = new Date(cellValue);
          var curr = new Date();
          var diff = (curr.getTime() - birthday.getTime())/(1000 * 60 * 60 * 24);  // 天
          if (diff >= 365) {
              return Math.floor(diff/365) + "岁";
          } else if (diff > 30 && diff < 365) {
              return Math.floor(diff/30) + "个月";
          } else {
              return Math.floor(diff/30) + "天";
          }
      },



      }
    };

</script>